# 感知状态滤波器配置示例
# 用于解决mark_perception_status频繁切换导致的摆头问题

# 滤波窗口大小 - 用于计算置信度的历史数据点数量
# 建议值：3-10，值越大越稳定但响应越慢
perception_filter_window_size: 5

# 置信度阈值 - 决定何时认为检测到信标
# 建议值：0.4-0.8，值越高要求越严格
# 例如：0.6表示60%的历史数据显示检测到信标时才认为真正检测到
perception_filter_confidence_threshold: 0.6

# 防抖时间(秒) - 状态切换的最小时间间隔
# 建议值：0.5-2.0秒，值越大切换越不频繁
# 例如：1.0表示状态改变后至少1秒才能再次改变
perception_filter_debounce_time: 1.0

# 使用说明：
# 1. 如果小车摆头频繁，可以：
#    - 增加 perception_filter_window_size (如改为7-8)
#    - 增加 perception_filter_confidence_threshold (如改为0.7-0.8)
#    - 增加 perception_filter_debounce_time (如改为1.5-2.0)
#
# 2. 如果小车响应太慢，可以：
#    - 减少 perception_filter_window_size (如改为3-4)
#    - 减少 perception_filter_confidence_threshold (如改为0.4-0.5)
#    - 减少 perception_filter_debounce_time (如改为0.5-0.8)
#
# 3. 推荐的调试步骤：
#    a) 先观察日志中的 "[PerceptionFilter] Status changed" 消息
#    b) 如果切换过于频繁，逐步增加参数值
#    c) 如果响应过慢，逐步减少参数值
#    d) 在实际环境中测试并微调
