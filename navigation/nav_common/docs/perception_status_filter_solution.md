# 感知状态滤波器解决方案

## 问题描述

在nav_cross_region和nav_mower节点中，由于感知数据`mark_loc_result.mark_perception_status`检测的不稳定，出现沿边控制EdgeFollowEnable和感知驱动控制PerceptionBasedAdjustment频繁切换的问题，导致小车出现摆头现象。

## 问题原因分析

1. **感知数据不稳定**: `mark_perception_status`在0（未检测到信标）和1（检测到信标）之间频繁切换
2. **直接状态切换**: 代码直接根据原始感知状态进行控制模式切换，没有滤波机制
3. **缺乏防抖机制**: 没有时间延迟来避免快速的状态变化

## 解决方案

### 1. 感知状态滤波器 (PerceptionStatusFilter)

实现了一个智能滤波器，具有以下特性：

- **滑动窗口滤波**: 使用历史数据计算置信度
- **置信度阈值**: 只有当置信度超过阈值时才认为状态改变
- **防抖机制**: 状态改变后需要等待一定时间才能再次改变

### 2. 核心算法

```cpp
// 计算检测置信度
float detection_confidence = detected_count / window_size;

// 根据置信度决定状态
int proposed_status = (detection_confidence >= threshold) ? 1 : 0;

// 防抖逻辑
if (proposed_status != last_stable_status && time_elapsed >= debounce_time) {
    last_stable_status = proposed_status;
}
```

### 3. 参数配置

| 参数 | 默认值 | 说明 | 调优建议 |
|------|--------|------|----------|
| `perception_filter_window_size` | 5 | 滑动窗口大小 | 摆头严重时增加到7-8 |
| `perception_filter_confidence_threshold` | 0.6 | 置信度阈值 | 摆头严重时增加到0.7-0.8 |
| `perception_filter_debounce_time` | 1.0s | 防抖时间 | 摆头严重时增加到1.5-2.0s |

## 实现细节

### 1. 文件结构

```
navigation/nav_common/include/perception_status_filter.hpp  # 滤波器类
navigation/nav_common/include/data_type.hpp                # 添加filtered_mark_perception_status字段
navigation/nav_mower/src/mower.cpp                         # 应用滤波器
navigation/nav_cross_region/src/cross_region.cpp           # 应用滤波器
config/perception_filter_config_example.yaml               # 配置示例
```

### 2. 关键修改

1. **数据结构扩展**:
   ```cpp
   struct MarkLocationResult {
       int mark_perception_status;           // 原始状态
       int filtered_mark_perception_status;  // 滤波后状态
       // ...
   };
   ```

2. **滤波器应用**:
   ```cpp
   // 应用滤波器
   mark_loc_result.filtered_mark_perception_status = 
       perception_status_filter_.UpdateAndFilter(mark_loc_result.mark_perception_status);
   
   // 使用滤波后的状态进行控制决策
   if (mark_loc_result.filtered_mark_perception_status == 0) {
       // 启用沿边控制
       EdgeFollowEnable();
   } else {
       // 启用感知驱动控制
       PerceptionBasedAdjustment();
   }
   ```

## 使用方法

### 1. 编译和部署

确保新的头文件和源文件被正确编译到项目中。

### 2. 参数调优

根据实际环境调整参数：

**如果摆头仍然频繁**:
- 增加 `perception_filter_window_size` (如7-8)
- 增加 `perception_filter_confidence_threshold` (如0.7-0.8)
- 增加 `perception_filter_debounce_time` (如1.5-2.0s)

**如果响应过慢**:
- 减少 `perception_filter_window_size` (如3-4)
- 减少 `perception_filter_confidence_threshold` (如0.4-0.5)
- 减少 `perception_filter_debounce_time` (如0.5-0.8s)

### 3. 监控和调试

观察日志中的滤波器状态变化：
```
[PerceptionFilter] Status changed to 1 (confidence: 0.80, window_size: 5)
```

## 测试验证

提供了测试程序 `test_perception_status_filter.cpp` 来验证滤波器功能：

```bash
# 编译测试程序
g++ -std=c++17 navigation/nav_common/test/test_perception_status_filter.cpp -o test_filter

# 运行测试
./test_filter
```

## 预期效果

1. **减少摆头**: 通过滤波和防抖机制显著减少控制模式的频繁切换
2. **提高稳定性**: 感知数据的短暂波动不会立即影响控制决策
3. **保持响应性**: 在稳定性和响应性之间找到平衡
4. **可配置性**: 可根据不同环境和需求调整参数

## 注意事项

1. 参数需要根据实际环境进行调优
2. 过于严格的参数可能导致响应延迟
3. 建议在实际环境中逐步调整参数
4. 监控日志以了解滤波器的工作状态
