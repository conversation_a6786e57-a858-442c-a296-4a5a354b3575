#ifndef PERCEPTION_STATUS_FILTER_HPP
#define PERCEPTION_STATUS_FILTER_HPP

#include "utils/logger.hpp"

#include <chrono>
#include <vector>

namespace fescue_iox
{

/**
 * @brief 感知状态滤波器，用于解决mark_perception_status频繁切换导致的摆头问题
 */
class PerceptionStatusFilter
{
public:
    PerceptionStatusFilter(int filter_window_size = 5, float confidence_threshold = 0.6f,
                           float debounce_time_s = 1.0f)
        : filter_window_size_(filter_window_size)
        , confidence_threshold_(confidence_threshold)
        , debounce_time_s_(debounce_time_s)
        , last_stable_status_(0)
        , last_status_change_time_(std::chrono::steady_clock::now())
    {
        status_history_.reserve(filter_window_size_);
    }

    /**
     * @brief 更新感知状态并返回滤波后的状态
     * @param raw_status 原始感知状态
     * @return 滤波后的稳定状态
     */
    int UpdateAndFilter(int raw_status)
    {
        auto current_time = std::chrono::steady_clock::now();

        // 添加到历史记录
        status_history_.push_back(raw_status);
        if (static_cast<int>(status_history_.size()) > filter_window_size_)
        {
            status_history_.erase(status_history_.begin());
        }

        // 计算当前状态的置信度
        int detected_count = 0;
        for (int status : status_history_)
        {
            if (status != 0)
                detected_count++;
        }

        float detection_confidence = static_cast<float>(detected_count) / status_history_.size();

        // 根据置信度和防抖时间决定是否切换状态
        int proposed_status = (detection_confidence >= confidence_threshold_) ? 1 : 0;

        // 防抖逻辑：只有在状态真正改变且经过足够时间后才切换
        if (proposed_status != last_stable_status_)
        {
            auto time_since_last_change = std::chrono::duration<float>(current_time - last_status_change_time_).count();
            if (time_since_last_change >= debounce_time_s_)
            {
                last_stable_status_ = proposed_status;
                last_status_change_time_ = current_time;
                LOG_INFO("[PerceptionFilter] Status changed to {} (confidence: {:.2f}, window_size: {})",
                         last_stable_status_, detection_confidence, status_history_.size());
            }
        }

        return last_stable_status_;
    }

    /**
     * @brief 重置滤波器状态
     */
    void Reset()
    {
        status_history_.clear();
        last_stable_status_ = 0;
        last_status_change_time_ = std::chrono::steady_clock::now();
    }

    /**
     * @brief 获取当前检测置信度
     */
    float GetCurrentConfidence() const
    {
        if (status_history_.empty())
            return 0.0f;

        int detected_count = 0;
        for (int status : status_history_)
        {
            if (status != 0)
                detected_count++;
        }
        return static_cast<float>(detected_count) / status_history_.size();
    }

private:
    int filter_window_size_;                                        // 滤波窗口大小
    float confidence_threshold_;                                    // 置信度阈值
    float debounce_time_s_;                                         // 防抖时间(秒)
    std::vector<int> status_history_;                               // 状态历史记录
    int last_stable_status_;                                        // 上次稳定状态
    std::chrono::steady_clock::time_point last_status_change_time_; // 上次状态改变时间
};

} // namespace fescue_iox

#endif // PERCEPTION_STATUS_FILTER_HPP
