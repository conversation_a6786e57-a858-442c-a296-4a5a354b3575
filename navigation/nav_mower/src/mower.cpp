#include "mower.hpp"

#include "mower_config.hpp"
#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <thread>
#include <utility>

#define TEST 0

using namespace mower_msgs::msg;

namespace fescue_iox
{

NavigationMowerAlg::NavigationMowerAlg(const MowerAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("MowerAlg"))
{
    last_cooldown_time_ = std::chrono::steady_clock::now();
    last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
    last_mark_detection_time_ = std::chrono::steady_clock::now();
    last_grass_time_ = std::chrono::steady_clock::now();
    last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
    last_zero_velocity_time_ = std::chrono::steady_clock::now();
    exception_start_time_ = std::chrono::steady_clock::now();

    beacon_status_ = BeaconStatus(-1, 0);
    cross_recharge_beacon_status_ = BeaconStatus(-1, 0);

    {
        master_region_explore_result_.is_exist = false;
        master_region_explore_result_.area = 0.0;
        master_region_explore_result_.perimeter = 0.0;
        master_region_explore_result_.charge_station_flag = false;
        master_region_explore_result_.beacon_id = -1;

        slave_region_explore_result_.is_exist = false;
        slave_region_explore_result_.area = 0.0;
        slave_region_explore_result_.perimeter = 0.0;
        slave_region_explore_result_.charge_station_flag = false;
        slave_region_explore_result_.beacon_id = -1;
    }

    SetMowerAlgParam(param);
    InitPublisher();

    // Initialize data time information map
    {
        std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
        data_time_info_map_.clear();
        uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
        DataTimeInfo data_time_info;
        // Initialize receive timestamp to current time
        data_time_info.recv_timestamp = now_timestamp;
        // Initialize send timestamp to 0
        data_time_info.send_timestamp = 0;
        data_time_info.is_low_freq = false;
        data_time_info.low_freq_count = 0;
        data_time_info.is_timeout = false;

        // Initialize all data sources
        data_time_info_map_["PerceptionFusion"] = data_time_info;
        data_time_info_map_["MarkLocation"] = data_time_info;
        data_time_info_map_["QRCodeLocation"] = data_time_info;
        data_time_info_map_["ImuData"] = data_time_info;
        data_time_info_map_["McuException"] = data_time_info;
        data_time_info_map_["MotorSpeed"] = data_time_info;
    }

#if STUCK
    InitStuckDetectionRecovery();
#endif
    PoseStuckConfig pose_stuck_config;
    pose_stuck_detector_ = std::make_unique<PoseStuckDetector>(pose_stuck_config);
    front_bev_blind_spot_ = GetFrontBEVBlindSpot();

    // Initialize IMU data processor
    InitializeImuProcessor();

    // 初始化感知状态滤波器
    perception_status_filter_ = PerceptionStatusFilter(
        perception_filter_window_size_,
        perception_filter_confidence_threshold_,
        perception_filter_debounce_time_);
}

NavigationMowerAlg::~NavigationMowerAlg()
{
#if STUCK
    DeinitStuckDetectionRecovery();
#endif

    // Shutdown IMU processor
    ShutdownImuProcessor();

    LOG_WARN("NavigationMowerAlg exit!");
}

void NavigationMowerAlg::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_speed_mtx_);
    motor_speed_data_ = motor_speed_data;

    // Pass motor data to stuck detection system
    if (stuck_detection_recovery_)
    {
        stuck_detection_recovery_->SetMotorSpeedData(motor_speed_data);
    }

    GetVelocityFromMotorRPM(motor_speed_data.motor_speed_left,
                            motor_speed_data.motor_speed_right,
                            wheel_radius_, wheel_base_, act_linear_, act_angular_);

    if (imu_processor_)
    {
        imu_processor_->SetMotorSpeedData(act_linear_, act_angular_);
    }

#if 0
    // Update data time information for validation
    SetMotorSpeedDataWithTimeInfo(motor_speed_data);

#endif
}

void NavigationMowerAlg::SetMotionDetectionResult(const MotionDetectionResult &motion_detection_result)
{
    std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
    motion_detection_result_ = motion_detection_result;
    // LOG_INFO_THROTTLE(1000, "[MowerAlg] [SetMotionDetectionResult] is_motion({})", motion_detection_result.is_motion);
}

void NavigationMowerAlg::SetImuData(const ImuData &imu_data)
{
    std::lock_guard<std::mutex> lock(imu_data_mtx_);
    imu_data_ = imu_data;

    // Pass IMU data to stuck detection system
    if (stuck_detection_recovery_)
    {
        stuck_detection_recovery_->SetImuData(imu_data);
    }

    // Pass IMU data to IMU processor
    if (imu_processor_)
    {
        imu_processor_->SetImuData(imu_data);
    }
}

void NavigationMowerAlg::SetRawImuData(const ImuData &raw_imu_data)
{
    std::lock_guard<std::mutex> lock(raw_imu_data_mtx_);
    raw_imu_data_ = raw_imu_data;
}

bool NavigationMowerAlg::IsWheelSlipping(const MotorSpeedData &motor_data,
                                         const MotionDetectionResult &motion_detection_result,
                                         float wheel_radius, float wheel_base)
{
    if (motor_data.system_timestamp < last_imu_timestamp_)
    {
        LOG_ERROR("[MowerAlg] [IsWheelSlipping1] Timestamp difference is less than 0");
        return false;
    }

    last_imu_timestamp_ = motor_data.system_timestamp;
    motion_detection_timestamp_ = motion_detection_result.timestamp;

    static bool is_slipping = false; // Track current slip status
    static int slip_counter = 0;
    const int slip_threshold = 200;

    // Convert motor speed from RPM to rad/s
    float w_left = motor_data.motor_speed_left * 2 * M_PI / 60.0f;
    float w_right = motor_data.motor_speed_right * 2 * M_PI / 60.0f;
    // LOG_ERROR("[MowerAlg] [IsWheelSlipping1] w_left({}), w_right({})", w_left, w_right);

    float v_left = w_left * wheel_radius;
    float v_right = w_right * wheel_radius;

    float act_linear = (v_right + v_left) / 2.0f;
    float act_angular = (v_right - v_left) / wheel_base;

    // bool is_data_synced =
    //     std::abs(static_cast<int64_t>(motor_data.system_timestamp - motion_detection_result.timestamp)) < 1000; // ms

    // if (!is_data_synced)
    // {
    //     LOG_ERROR("[MowerAlg] [IsWheelSlipping1] Time sync check failed");
    //     return false;
    // }

    bool is_has_speed = (fabs(act_linear) > min_valid_linear_) || (fabs(act_angular) > min_valid_angular_);
    bool potential_slip = is_has_speed && !motion_detection_result.is_motion;

    if (!is_slipping)
    {
        if (potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = true;
            slip_counter = 0;
            LOG_INFO_THROTTLE(1000, "[MowerAlg] [IsWheelSlipping1] Slipping status detected");
        }
    }
    else // is_slipping == true
    {
        if (!potential_slip)
        {
            slip_counter++;
        }
        else
        {
            slip_counter = 0;
        }
        if (slip_counter >= slip_threshold)
        {
            is_slipping = false;
            slip_counter = 0;
            LOG_INFO_THROTTLE(1000, "[MowerAlg] [IsWheelSlipping1] Slipping status ended");
        }
    }

    LOG_INFO_THROTTLE(5000, "[MowerAlg] [IsWheelSlipping1] act_linear({}), act_angular({}), is_motion({}), is_slipping({}), slip_counter({})",
                      act_linear, act_angular, motion_detection_result.is_motion, is_slipping, slip_counter);

    return is_slipping;
}

void NavigationMowerAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

const char *NavigationMowerAlg::GetVersion()
{
    return "V1.1.0";
}

void NavigationMowerAlg::SetChargePileDockStatus(bool status)
{
    (void)status;
    // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] charge_pile_dock_status({})", status);
}

void NavigationMowerAlg::SetMCUSensor(const mower_msgs::msg::McuSensor &data)
{
    is_power_connected_ = data.charge_terminal_status;
    // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] charge_terminal_status({})", is_power_connected_);
}

void NavigationMowerAlg::SetMowerComplete(const bool &mower_completed)
{
    LOG_INFO_THROTTLE(1000, "[BeaconDetection1] [Run] Mower complete!");
    mower_completed_ = mower_completed;
}

void NavigationMowerAlg::SetSlopeDetectionResult(const SlopeDetectionResult &slope_detection_result)
{
    std::lock_guard<std::mutex> lock(slope_detection_result_mtx_);
    slope_detection_result_ = slope_detection_result;
}

void NavigationMowerAlg::SetGrassDetecteStatus(const GrassDetectStatus &data)
{
    grass_detect_status_ = data;
    std::string grass_status_str;
    switch (grass_detect_status_)
    {
    case GrassDetectStatus::NO_GRASS:
        grass_status_str = "NO_GRASS";
        break;
    case GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE:
        grass_status_str = "HAVE_GRASS_NO_OBSTACLE";
        break;
    case GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE:
        grass_status_str = "HAVE_GRASS_HAVE_OBSTACLE";
        break;
    default:
        grass_status_str = "UNKNOWN";
        break;
    }
    LOG_INFO_THROTTLE(1000, "[SetGrassDetecteStatus1] grass_detect_status_: {} ({})", int(grass_detect_status_), grass_status_str);

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (IsGrassField(grass_detect_status_)) // On grass field
        {
            is_on_grass_field_ = true;
            LOG_INFO_THROTTLE(1000, "[SetGrassDetecteStatus1] Is on grass field!");

            is_first_non_grass_detection_ = true; // Reset non-grass timer
            last_grass_time_ = std::chrono::steady_clock::now();
        }
        else // Not on grass field
        {
            is_on_grass_field_ = false;
            LOG_INFO_THROTTLE(1000, "[SetGrassDetecteStatus1] Is not on grass field!");

            auto current_time = std::chrono::steady_clock::now();
            if (is_first_non_grass_detection_)
            {
                last_grass_time_ = current_time;
                is_first_non_grass_detection_ = false;
            }
            non_grass_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_grass_time_);
            LOG_INFO_THROTTLE(1000, "[SetGrassDetecteStatus1] Non-grass duration: {} seconds", non_grass_duration_.count());
        }
    }
}

void NavigationMowerAlg::SetAllTaskClose()
{
    thread_control_ = ThreadControl::CLOSE_ALL_TASK;
    UpdateFeatureSelection(thread_control_);
    ResetMowerAlgFlags();
}

void NavigationMowerAlg::TestNonBlockingControl()
{
    if (!is_test_running_)
    {
        // Initialize test
        is_test_running_ = true;
        test_start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("[TestControl] Start non-blocking velocity control test");
    }

    // Calculate elapsed time
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(
                       std::chrono::steady_clock::now() - test_start_time_)
                       .count();

    if (elapsed < test_duration_ms_)
    {
        // Continuously send velocity commands (non-blocking)
        PublishVelocity(test_linear_speed_, test_angular_speed_, 0.0);
        LOG_INFO_THROTTLE(500, "[TestControl] Controlling... Remaining time: {} ms",
                          test_duration_ms_ - elapsed);
    }
    else
    {
        // Stop motion at the end of the test
        PublishVelocity(0.0, 0.0, 0.0);
        // is_test_running_ = false;
        LOG_INFO("[TestControl] Test completed, total time {} ms", test_duration_ms_);
    }
}

void NavigationMowerAlg::Test(CrossRegionRunningState cross_region_state)
{
    (void)cross_region_state;
    TestNonBlockingControl();
#if 0
    //! 1. First stage. Start spiral mowing function
    if (!mcu_triggers_cross_region_ &&
        !mcu_triggers_mower_ &&
        mcu_triggers_spiral_mower_)
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] Start spiral mowing mode!");
        thread_control_ = ThreadControl::SPIRAL_MOWING_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

    //! 2. Second stage. Start random mowing function. MCU does not issue cross-region or recharge request
    if (!mcu_triggers_cross_region_ && /* MCU no cross-region request */
        mcu_triggers_mower_)           /* MCU mowing request */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] Start mowing mode!");
        PerformRandomMowing();
    }

    //! 3. Third stage. MCU issues cross-region request
    if (mcu_triggers_cross_region_ && /* MCU cross-region request */
        !mcu_triggers_mower_)         /* MCU no mowing request */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] Start cross-region mode!");

        // Execute cross-channel thread, close edge-follow thread
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

#endif
}

MowerAlgResult NavigationMowerAlg::Run(const MarkLocationResult &mark_loc_result,
                                       CrossRegionRunningState cross_region_state,
                                       const QRCodeLocationResult &qrcode_loc_result,
                                       const PerceptionFusionResult &fusion_result,
                                       bool is_new_fusion,
                                       RechargeRunningState recharge_state,
                                       McuExceptionStatus &mcu_exception_status,
                                       BehaviorRunningState &behavior_state,
                                       bool is_behavior_loop,
                                       const std::vector<BehaviorExceptionType> &triggered_exception_types,
                                       RandomMowerRunningState &random_mower_state,
                                       const ChargeStationDetectResult &station_result)
{
    CalculateFusionPose();
    if (mower_running_state_ == MowerRunningState::PAUSE || mower_running_state_ == MowerRunningState::STOP)
    {
        ShowMowerRunningInfo(mark_loc_result);
        LOG_WARN_THROTTLE(3000, "Mower Alg Run() is PAUSE!");
        is_slipping_detected_.store(false);
        is_stuck_detected_.store(false);

        last_perception_movement_time_ = std::chrono::steady_clock::time_point{};
        last_bottom_obstacle_avoidance_time_ = std::chrono::steady_clock::time_point{};
        pose_stuck_detector_ = nullptr;

#if STUCK
        SetStuckDetectionActive(false);
        SetResetAllStuckStates();
        if (rolling_grid_map_ != nullptr)
        {
            rolling_grid_map_->Clear();
            rolling_grid_map_ = nullptr;
        }
        last_update_point_cloud_time_ = 0;
        is_new_dangerous_point_cloud_ = false;

#endif

        return MowerAlgResult(false);
    }

#if 0
    // Update data time information and validate all input sources
    if (!UpdateAndValidateInputData(mark_loc_result, qrcode_loc_result, fusion_result, mcu_exception_status))
    {
        return MowerAlgResult(false);
    }
#endif
    UpdatePoseStuckDetection(mcu_exception_status);

    UpdateRollingGridMap(fusion_result, is_new_fusion);
    if (mower_completed_)
    {
        return MowerAlgResult(true);
    }

#if (TEST == 1)

    ShowMowerRunningInfo(mark_loc_result);

    if (mcu_triggers_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_spiral_mower_)
    {
        Test(cross_region_state);
    }
    else
    {
        is_test_running_ = false;
    }
#else

    random_mower_state_ = random_mower_state;

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (is_region_explore_mode_start_)
        {
            float area = 0.0;
            float perimeter = 0.0;
            if (area_calc_stop_callback_)
            {
                area_calc_stop_callback_(GetSteadyClockTimestampMs(), area, perimeter);
            }

            RegionExploreResult region_explore_result;
            {
                region_explore_result.result = true;
                region_explore_result.timestamp = GetSteadyClockTimestampMs();
                region_explore_result.master_region_map_result = master_region_explore_result_;
                region_explore_result.slave_region_map_result = slave_region_explore_result_;
            }

            if (region_explore_result_callback_)
            {
                region_explore_result_callback_(region_explore_result);
            }

            is_region_explore_mode_start_ = false;
        }
    }

    if (mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        if (is_cut_border_mode_start_)
        {
            if (cut_border_result_callback_)
            {
                cut_border_result_callback_(true, true);
            }

            is_cut_border_mode_start_ = false;
        }
    }

    if (mcu_triggers_cut_border_ ||
        mcu_triggers_region_exploration_ ||
        mcu_triggers_mower_ ||
        mcu_triggers_spiral_mower_ ||
        mcu_triggers_cross_region_ ||
        mcu_triggers_recharge_)
    {
        HandleSelfCheckAndOperation(mark_loc_result, cross_region_state, qrcode_loc_result,
                                    fusion_result, recharge_state, mcu_exception_status,
                                    behavior_state, is_behavior_loop, triggered_exception_types, station_result);
    }

#endif

    return MowerAlgResult(mower_completed_);
}

void NavigationMowerAlg::UpdateRollingGridMap(const PerceptionFusionResult &fusion_result, bool is_new_fusion)
{
    UpdateRollingGridMapPose();
    if (is_new_fusion)
    {
        UpdateRollingGridMapObs(fusion_result.occupancy_grid);
    }
    uint64_t cur_time = GetSteadyClockTimestampMs();
    uint64_t update_time_interval = 100;
    if (last_update_point_cloud_time_ == 0 || cur_time - last_update_point_cloud_time_ > update_time_interval)
    {
        last_update_point_cloud_time_ = cur_time;
        Pose2f cur_pose(fusion_pose_.x, fusion_pose_.y, fusion_pose_.yaw);
        auto occupied_points = rolling_grid_map_->GetOccupiedPoints();
        // lock and update dangerous point cloud
        std::lock_guard<std::mutex> lock(dangerous_point_cloud_mtx_);
        dangerous_point_cloud_.clear();
        float cos_theta = std::cos(cur_pose.theta);
        float sin_theta = std::sin(cur_pose.theta);
        for (size_t i = 0; i < occupied_points.size(); i++)
        {
            const auto &point = occupied_points[i];
            float robot_x = (point.x - cur_pose.x) * cos_theta + (point.y - cur_pose.y) * sin_theta;
            float robot_y = -(point.x - cur_pose.x) * sin_theta + (point.y - cur_pose.y) * cos_theta;
            dangerous_point_cloud_.emplace_back(robot_x, robot_y);
        }
        dangerous_point_cloud_pose_ = cur_pose;
        is_new_dangerous_point_cloud_ = true;
    }
}

void NavigationMowerAlg::UpdateRollingGridMapPose()
{
    if (rolling_grid_map_ == nullptr)
    {
        rolling_grid_map_ = std::make_shared<RollingGridMap>();
    }
    Pose2f pose(fusion_pose_.x, fusion_pose_.y, fusion_pose_.yaw);
    rolling_grid_map_->UpdatePose(pose);
}

void NavigationMowerAlg::UpdateRollingGridMapObs(const OccupancyResult &occupancy_result)
{
    if (rolling_grid_map_ == nullptr)
    {
        return;
    }
    std::unordered_set<uint8_t> obs_values;
    uint8_t mark_type = static_cast<uint8_t>(fescue_msgs_enum__GrassCellType::FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_MARK);
    obs_values.insert(mark_type);
    float confident_x_min = 0;
    float confident_x_max = 1.85;
    float confident_y_min = -0.4;
    float confident_y_max = 0.4;
    auto [grid_map_base, robot_map_data] = fescue_iox::GetRobotMapData(occupancy_result, confident_x_min, confident_x_max, confident_y_min, confident_y_max, obs_values);
    // update obstacle
    for (const auto &[robot_point, is_obstacle] : robot_map_data)
    {
        rolling_grid_map_->UpdateObstacle(robot_point.x, robot_point.y, is_obstacle);
    }
    // update blind spot
    for (const auto &point : front_bev_blind_spot_)
    {
        rolling_grid_map_->UpdateObstacle(point.x, point.y, false);
    }
}

DangerousPointCloud NavigationMowerAlg::GetDangerousPointCloud()
{
    std::lock_guard<std::mutex> lock(dangerous_point_cloud_mtx_);
    DangerousPointCloud dangerous_point_cloud;
    dangerous_point_cloud.point_cloud = dangerous_point_cloud_;
    dangerous_point_cloud.pose = dangerous_point_cloud_pose_;
    dangerous_point_cloud.is_new = is_new_dangerous_point_cloud_;
    is_new_dangerous_point_cloud_ = false;
    return dangerous_point_cloud;
}

//==============================================================================
// Self Checking
//==============================================================================

void NavigationMowerAlg::HandleSelfCheckAndOperation(const MarkLocationResult &mark_loc_result,
                                                     CrossRegionRunningState cross_region_state,
                                                     const QRCodeLocationResult &qrcode_loc_result,
                                                     const PerceptionFusionResult &fusion_result,
                                                     RechargeRunningState recharge_state,
                                                     McuExceptionStatus &mcu_exception_status,
                                                     BehaviorRunningState &behavior_state,
                                                     bool is_behavior_loop,
                                                     const std::vector<BehaviorExceptionType> &triggered_exception_types,
                                                     const ChargeStationDetectResult &station_result)
{
    if (!is_self_checking_)
    {
        is_self_checking_ = true;
        is_self_recovery_active_ = false;
        self_check_start_time_ = std::chrono::steady_clock::now();
        LOG_INFO("[BeaconDetection1] Start self-check...");

        SetUndockResult(false, false, mower_msgs::srv::UndockOperationStatus::NOT_INTERRUPTIBLE);

        if (area_calc_start_callback_)
        {
            area_calc_start_callback_(GetSteadyClockTimestampMs());
        }
    }
    else
    {
        auto current_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::seconds>(current_time - self_check_start_time_);

        if (is_self_recovery_active_)
        {
            LOG_INFO("[BeaconDetection1] Self-check recovery in progress...");
            return;
        }
        else if (duration.count() >= SELF_CHECK_DURATION_SECONDS)
        {
            if (is_self_success_)
            {
                NormalOperation(mark_loc_result, cross_region_state, qrcode_loc_result,
                                fusion_result, recharge_state, mcu_exception_status,
                                behavior_state, is_behavior_loop, triggered_exception_types, station_result);
            }
            else
            {
                LOG_WARN("[BeaconDetection1] Initial self-check failed, starting self-check recovery process");

                is_self_recovery_active_ = true;

                std::thread recovery_thread([this]() {
                    bool recovery_result = PerformSelfCheckRecovery();

                    is_self_recovery_active_ = false;
                    is_self_success_ = recovery_result;

                    if (recovery_result)
                    {
                        LOG_INFO("[BeaconDetection1] Self-check recovery succeeded, will continue normal operation");
                    }
                    else
                    {
                        LOG_WARN("[BeaconDetection1] Self-check recovery failed, will not perform normal operation");
                    }
                });

                recovery_thread.detach();
            }
        }
        else
        {
            LOG_INFO("[BeaconDetection1] Self-check in progress, remaining time: {} seconds", SELF_CHECK_DURATION_SECONDS - duration.count());
            is_self_success_ = SelfChecking();
        }
    }
}
bool NavigationMowerAlg::SelfChecking()
{
    // 1. Check for abnormal state when not charging
    // 2. Check for abnormal state when not on grass
    if (!is_power_connected_ &&
        !is_on_grass_field_ && non_grass_duration_.count() >= SELF_CHECK_NON_GRASS_THRESHOLD_SECONDS)
    {
        LOG_WARN("[BeaconDetection1] Detected non-grass for more than {} seconds during self-check", SELF_CHECK_NON_GRASS_THRESHOLD_SECONDS);

        return false;
    }

    return true;
}

void NavigationMowerAlg::CalculateFusionPose()
{
    fusion_pose_ = {};
    uint64_t timestamp_ms = GetSteadyClockTimestampMs();
    double time_now = 1.0 * timestamp_ms / 1000.0;
    MotorSpeedData motor_speed_data;
    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        motor_speed_data = motor_speed_data_;
    }
    ImuData imu_data;
    {
        std::lock_guard<std::mutex> lock(raw_imu_data_mtx_);
        imu_data = raw_imu_data_;
    }

    fusion_data_.time = time_now;
    fusion_data_.imu_data = imu_data;
    fusion_data_.motor_speed_data = motor_speed_data;

    if (pose_fusion_ == nullptr)
    {
        if (is_imu_calibration_valid_)
        {
            pose_fusion_ = std::make_shared<PoseFusion>(imu_calibration_config_);
        }
        else
        {
            pose_fusion_ = std::make_shared<PoseFusion>();
        }
    }

    MotionDetectionResult motion_detection_result;
    {
        std::lock_guard<std::mutex> lock(motion_detection_result_mtx_);
        motion_detection_result = motion_detection_result_;
    }
    SlopeDetectionResult slope_detection_result;
    {
        std::lock_guard<std::mutex> lock(slope_detection_result_mtx_);
        slope_detection_result = slope_detection_result_;
    }

    {
        acceleration_filter_data_.ax_filter_val = pose_fusion_->GetAxFilterValue();
        acceleration_filter_data_.ay_filter_val = pose_fusion_->GetAyFilterValue();
        acceleration_filter_data_.ax_window.clear();
        acceleration_filter_data_.ay_window.clear();
        const auto &ax_error_window = pose_fusion_->GetAxErrorWindow();
        const auto &ay_error_window = pose_fusion_->GetAyErrorWindow();
        for (const auto &data : ax_error_window)
        {
            acceleration_filter_data_.ax_window.push_back(data);
        }
        for (const auto &data : ay_error_window)
        {
            acceleration_filter_data_.ay_window.push_back(data);
        }
    }
    fusion_data_.motion_detection_result = motion_detection_result;
    fusion_data_.slope_detection_result = slope_detection_result;
    fusion_data_.acceleration_filter_data = acceleration_filter_data_;
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        pose_fusion_->ResetVelocity();
    }
    pose_fusion_->Update(time_now, imu_data.linear_acceleration_x, imu_data.linear_acceleration_y, imu_data.linear_acceleration_z,
                         imu_data.angular_velocity_x, imu_data.angular_velocity_y, imu_data.angular_velocity_z,
                         motor_speed_data.motor_speed_left, motor_speed_data.motor_speed_right,
                         motor_speed_data.current_left, motor_speed_data.current_right,
                         motion_detection_result.is_motion, slope_detection_result.pitch, slope_detection_result.roll, slope_detection_result.yaw);

    fusion_pose_.timestamp_ms = timestamp_ms;
    fusion_pose_.x = pose_fusion_->GetX();
    fusion_pose_.y = pose_fusion_->GetY();
    fusion_pose_.yaw = pose_fusion_->GetYaw();
    fusion_pose_.pitch = pose_fusion_->GetPitch();
    fusion_pose_.roll = pose_fusion_->GetRoll();
    fusion_pose_.linear_velocity = pose_fusion_->GetLinearVelocity();
    fusion_pose_.angular_velocity = pose_fusion_->GetAngularVelocity();

    double moving_slip_ratio = pose_fusion_->GetMovingSlipRatio();
    double turning_slip_ratio = pose_fusion_->GetTurningSlipRatio();
    bool is_wheel_slip = pose_fusion_->GetIsWheelSlip();
    double slip_ratio = 1.0;
    fusion_pose_.is_slipping = false;
    bool need_detect_slip = false;
    fescue_msgs_enum_FunctionState function_state;
    uint64_t function_state_timestamp;
    {
        std::lock_guard<std::mutex> lock(function_state_mtx_);
        function_state = function_state_;
        function_state_timestamp = function_state_timestamp_;
    }
    if (function_state == fescue_msgs_enum_FunctionState::FUNCTION_STATE_RANDOM ||
        function_state == fescue_msgs_enum_FunctionState::FUNCTION_STATE_EDGE_FOLLOW ||
        function_state == fescue_msgs_enum_FunctionState::FUNCTION_STATE_BEHAVIOR)
    {
        uint64_t detect_slip_interval_ms = 500;
        uint64_t time_diff = timestamp_ms - function_state_timestamp;
        if (time_diff < detect_slip_interval_ms)
        {
            need_detect_slip = true;
        }
    }
    LOG_INFO_THROTTLE(1000, "need_detect_slip: {}, is_wheel_slip: {}, moving_slip_ratio: {}, turning_slip_ratio: {} linear: {} angular: {} function_state: {}",
                      need_detect_slip, is_wheel_slip, moving_slip_ratio, turning_slip_ratio,
                      fusion_pose_.linear_velocity, fusion_pose_.angular_velocity,
                      static_cast<int>(function_state));
    if (need_detect_slip && (is_wheel_slip || (moving_slip_ratio > slip_ratio - 1e-6) || (turning_slip_ratio > slip_ratio - 1e-6)))
    {
        LOG_INFO("wheel slip: {}, moving slip ratio: {}, turning slip ratio: {}", is_wheel_slip, moving_slip_ratio, turning_slip_ratio);
        is_slipping_detected_.store(true);
        fusion_pose_.is_slipping = true;
    }
}

void NavigationMowerAlg::SetImuCalibrationConfig(const IMUCalibrationConfig &config)
{
    is_imu_calibration_valid_ = true;
    imu_calibration_config_ = config;
    LOG_INFO("soc imu qx: {}, qy: {}, qz: {}, qw: {}, acc_bias_x: {}, acc_bias_y: {}, acc_bias_z: {}, gyro_bias_x: {}, gyro_bias_y: {}, gyro_bias_z: {}",
             imu_calibration_config_.qx, imu_calibration_config_.qy, imu_calibration_config_.qz, imu_calibration_config_.qw,
             imu_calibration_config_.acc_bias_x, imu_calibration_config_.acc_bias_y, imu_calibration_config_.acc_bias_z,
             imu_calibration_config_.gyro_bias_x, imu_calibration_config_.gyro_bias_y, imu_calibration_config_.gyro_bias_z);
}

void NavigationMowerAlg::SetFunctionState(const fescue_msgs_enum_FunctionState &function_state)
{
    std::lock_guard<std::mutex> lock(function_state_mtx_);
    function_state_ = function_state;
    function_state_timestamp_ = GetSteadyClockTimestampMs();
}

bool NavigationMowerAlg::PerformSelfCheckRecovery()
{
    auto recovery_duration_ms = static_cast<int64_t>(1000.0 * self_recovery_distance_ / std::abs(self_recovery_linear_speed_));

    LOG_INFO("[BeaconDetection1] Self-check failed, start reversing {} meters, expected duration {} ms",
             self_recovery_distance_, recovery_duration_ms);

    PublishVelocity(self_recovery_linear_speed_, 0.0);

    auto recovery_start_time = std::chrono::steady_clock::now();

    auto last_check_time = std::chrono::steady_clock::now();

    while (true)
    {
        auto current_time = std::chrono::steady_clock::now();
        auto elapsed_ms = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - recovery_start_time).count();
        LOG_INFO("[BeaconDetection1] Self-check in progress, reversed {} ms", elapsed_ms);

        PublishVelocity(self_recovery_linear_speed_, 0.0);

        if (elapsed_ms >= recovery_duration_ms)
        {
            PublishVelocity(0.0, 0.0, 1000);
            LOG_INFO("[BeaconDetection1] Reverse completed, total reversed {} ms", elapsed_ms);

            if (!SelfChecking())
            {
                LOG_WARN("[BeaconDetection1] Self-check still failed after reversing, reporting exception");
                SetAllTaskClose();

                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_SELF_CHECK_FAILED_EXCEPTION);
                return false;
            }
            else
            {
                LOG_INFO("[BeaconDetection1] Self-check succeeded after reversing");
                return true;
            }
        }

        if (std::chrono::duration_cast<std::chrono::milliseconds>(
                current_time - last_check_time)
                .count() >= 100)
        {
            last_check_time = current_time;

            if (SelfChecking())
            {
                PublishVelocity(0.0, 0.0, 1000);
                LOG_INFO("[BeaconDetection1] Self-check succeeded during reversing, reversed {} ms", elapsed_ms);
                return true;
            }
            else
            {
                LOG_WARN("[BeaconDetection1] Self-check failed during reversing, reversed {} ms", elapsed_ms);
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    return false;
}
//==============================================================================
// Self Checking
//==============================================================================

void NavigationMowerAlg::NormalOperation(const MarkLocationResult &mark_loc_result,
                                         CrossRegionRunningState cross_region_state,
                                         const QRCodeLocationResult &qrcode_loc_result,
                                         const PerceptionFusionResult &fusion_result,
                                         RechargeRunningState recharge_state,
                                         McuExceptionStatus &mcu_exception_status,
                                         BehaviorRunningState &behavior_state,
                                         bool is_behavior_loop,
                                         const std::vector<BehaviorExceptionType> &triggered_exception_types,
                                         const ChargeStationDetectResult &station_result)
{
    ShowExplorePrint(behavior_state);
    ShowMowerRunningInfo(mark_loc_result);

#if STUCK
    SetStuckDetectionActive(true);
#endif

    if (mcu_triggers_cut_border_)
    {
        is_cut_border_mode_start_ = true;
        CutBorderModule(mark_loc_result, cross_region_state,
                        qrcode_loc_result, fusion_result, recharge_state,
                        mcu_exception_status, behavior_state, is_behavior_loop, triggered_exception_types);
    }
    else
    {
        if (mcu_triggers_region_exploration_) // Region exploration
        {
            is_region_explore_mode_start_ = true;
            RegionExplorationModule(mark_loc_result, cross_region_state,
                                    qrcode_loc_result, fusion_result, recharge_state,
                                    mcu_exception_status, behavior_state, is_behavior_loop, triggered_exception_types);
        }
        else // Normal operation logic
        {
            NormalMowingModule(mark_loc_result, cross_region_state, qrcode_loc_result,
                               fusion_result, recharge_state, mcu_exception_status,
                               behavior_state, is_behavior_loop, triggered_exception_types, station_result);
        }
    }
}

void NavigationMowerAlg::CutBorderModule(const MarkLocationResult &mark_loc_result,
                                         CrossRegionRunningState cross_region_state,
                                         const QRCodeLocationResult &qrcode_loc_result,
                                         const PerceptionFusionResult &fusion_result,
                                         RechargeRunningState recharge_state,
                                         McuExceptionStatus &mcu_exception_status,
                                         BehaviorRunningState &behavior_state,
                                         bool is_behavior_loop,
                                         const std::vector<BehaviorExceptionType> &triggered_exception_types)
{
    (void)fusion_result;
    // Step 1, un-dock
    // ProcessCutBorderUnstakeMode();
    ProcessCutBorderUnstakeMode(qrcode_loc_result);

    // Initialize only on first entry
    if (is_first_enter_cut_border_last_qr_detection_time_)
    {
        last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now(); // After un-docking, start QR code detection timing
        is_first_enter_cut_border_last_qr_detection_time_ = false;

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

#if STUCK
    // Check stuck state
    LOG_INFO_THROTTLE(1000, "[CutBorderModule1] IsStuckDetected: {}, IsStuckRecoveryActive: {}, ShouldPerformStuckDetection: {}",
                      IsStuckDetected(), IsStuckRecoveryActive(), ShouldPerformStuckDetection());
    if (ShouldPerformStuckDetection() && IsStuckDetected() && !IsStuckRecoveryActive())
    {
        LOG_WARN("[CutBorderModule1] Stuck detected, starting recovery");
        StartStuckRecovery();
    }

    // If in recovery, check if finished
    if (IsStuckRecoveryActive())
    {
        LOG_INFO_THROTTLE(1000, "[CutBorderModule1] Stuck recovery in progress...");
        return;
    }

#endif

    bool is_slipping = is_slipping_detected_.load();
    bool is_stuck = is_stuck_detected_.load();
    ExceptionInfo exception_info;
    exception_info.recharge_state = recharge_state;
    exception_info.cross_region_state = cross_region_state;
    exception_info.is_slipping = is_slipping;
    exception_info.mcu_exception_status = mcu_exception_status;
    exception_info.is_stuck = is_stuck;
    if (is_slipping || is_stuck)
    {
        is_slipping_detected_.store(false);
        is_stuck_detected_.store(false);
        HandleCutBorderMcuException(exception_info);
        LOG_INFO_THROTTLE(1000, "[CutBorderModule1] Slipping or stuck detected, entering exception handling");
    }
    else
    {
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleCutBorderMcuException(exception_info);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            // Cut border function
            PerformCutBorder(mark_loc_result, qrcode_loc_result, fusion_result, cross_region_state, behavior_state, is_behavior_loop, triggered_exception_types);
            break;
        }
        default:
            break;
        }
    }
}

void NavigationMowerAlg::RegionExplorationModule(const MarkLocationResult &mark_loc_result,
                                                 CrossRegionRunningState cross_region_state,
                                                 const QRCodeLocationResult &qrcode_loc_result,
                                                 const PerceptionFusionResult &fusion_result,
                                                 RechargeRunningState recharge_state,
                                                 McuExceptionStatus &mcu_exception_status,
                                                 BehaviorRunningState &behavior_state,
                                                 bool is_behavior_loop,
                                                 const std::vector<BehaviorExceptionType> &triggered_exception_types)
{
    (void)fusion_result;
    // Step 1, un-dock. No need to handle MCU exception state
    // ProcessExplorationUnstakeMode();
    ProcessExplorationUnstakeMode(qrcode_loc_result);

    // Initialize only on first entry
    if (is_first_enter_explore_last_qr_detection_time_)
    {
        last_qr_explore_detection_time_ = std::chrono::steady_clock::now(); // After un-docking, start QR code detection timing
        is_first_enter_explore_last_qr_detection_time_ = false;

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }

#if STUCK
    // Check stuck state
    LOG_INFO_THROTTLE(1000, "[RegionExplorationModule1] IsStuckDetected: {}, IsStuckRecoveryActive: {}, ShouldPerformStuckDetection: {}",
                      IsStuckDetected(), IsStuckRecoveryActive(), ShouldPerformStuckDetection());
    if (ShouldPerformStuckDetection() && IsStuckDetected() && !IsStuckRecoveryActive())
    {
        LOG_WARN("[RegionExplorationModule1] Stuck detected, starting recovery");
        StartStuckRecovery();
    }

    // If in recovery, check if finished
    if (IsStuckRecoveryActive())
    {
        LOG_INFO_THROTTLE(1000, "[RegionExplorationModule1] Stuck recovery in progress...");
        return;
    }
#endif

    bool is_slipping = is_slipping_detected_.load();
    bool is_stuck = is_stuck_detected_.load();
    ExceptionInfo exception_info;
    exception_info.recharge_state = recharge_state;
    exception_info.cross_region_state = cross_region_state;
    exception_info.is_slipping = is_slipping;
    exception_info.mcu_exception_status = mcu_exception_status;
    exception_info.is_stuck = is_stuck;
    if (is_slipping || is_stuck)
    {
        is_slipping_detected_.store(false);
        is_stuck_detected_.store(false);
        HandleExplorationMcuException(exception_info);
        LOG_INFO_THROTTLE(1000, "[RegionExplorationModule1] Slipping or stuck detected, entering exception handling");
    }
    else
    {
        // Handle different MCU exception states
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleExplorationMcuException(exception_info);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            // Region exploration function
            PerformExploration(mark_loc_result, qrcode_loc_result, fusion_result, cross_region_state, behavior_state, is_behavior_loop, triggered_exception_types);
            break;
        }
        default:
            break;
        }
    }
}

void NavigationMowerAlg::NormalMowingModule(const MarkLocationResult &mark_loc_result,
                                            CrossRegionRunningState cross_region_state,
                                            const QRCodeLocationResult &qrcode_loc_result,
                                            const PerceptionFusionResult &fusion_result,
                                            RechargeRunningState recharge_state,
                                            McuExceptionStatus &mcu_exception_status,
                                            BehaviorRunningState &behavior_state,
                                            bool is_behavior_loop,
                                            const std::vector<BehaviorExceptionType> &triggered_exception_types,
                                            const ChargeStationDetectResult &station_result)
{
    (void)fusion_result;
    if (is_power_connected_) // Connected to power
    {
        // ProcessNormalOperationUnstakeMode();
        ProcessNormalOperationUnstakeMode(qrcode_loc_result);
    }
    else // Not connected to power
    {
        if (!is_unstaking_)
        {
            PreProcessingMowing(qrcode_loc_result);
        }
    }

#if STUCK
    // Check stuck state
    LOG_INFO_THROTTLE(1000, "[NormalMowingModule1] IsStuckDetected: {}, IsStuckRecoveryActive: {}, ShouldPerformStuckDetection: {}",
                      IsStuckDetected(), IsStuckRecoveryActive(), ShouldPerformStuckDetection());
    if (ShouldPerformStuckDetection() && IsStuckDetected() && !IsStuckRecoveryActive()) // IsStuckDetected must be true, IsStuckRecoveryActive must be false
    {
        LOG_WARN("[NormalMowingModule1] Stuck detected, starting recovery");
        StartStuckRecovery();
    }

    // If in recovery, check if finished
    if (IsStuckRecoveryActive()) // IsStuckDetected can be false or true, IsStuckRecoveryActive must be true
    {
        LOG_INFO_THROTTLE(1000, "[NormalMowingModule1] Stuck recovery in progress...");
        return;
    }

#endif

    bool is_slipping = is_slipping_detected_.load();
    bool is_stuck = is_stuck_detected_.load();
    ExceptionInfo exception_info;
    exception_info.recharge_state = recharge_state;
    exception_info.cross_region_state = cross_region_state;
    exception_info.is_slipping = is_slipping;
    exception_info.mcu_exception_status = mcu_exception_status;
    exception_info.is_stuck = is_stuck;
    if (is_slipping || is_stuck)
    {
        is_slipping_detected_.store(false);
        is_stuck_detected_.store(false);
        LOG_INFO("is_slipping: {}, mcu_exception_status: {}", is_slipping, static_cast<int>(mcu_exception_status));
        HandleMcuException(exception_info);
        LOG_INFO_THROTTLE(1000, "[NormalMowingModule1] Slipping or stuck detected, entering exception handling");
    }
    else
    {
        // Handle different MCU exception states
        switch (mcu_exception_status)
        {
        case McuExceptionStatus::COLLISION:
        case McuExceptionStatus::LIFTING:
        {
            HandleMcuException(exception_info);
            break;
        }
        case McuExceptionStatus::NORMAL:
        {
            HandleNormalCrossRegionStates(cross_region_state);

            HandleNormalOperation(cross_region_state, recharge_state, behavior_state,
                                  qrcode_loc_result, mark_loc_result, fusion_result, station_result, is_behavior_loop, triggered_exception_types);
            break;
        }
        default:
            break;
        }
    }
}

//==============================================================================
// Region exploration function handlers
//==============================================================================
void NavigationMowerAlg::PerformExploration(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                                            const PerceptionFusionResult &fusion_result,
                                            CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state,
                                            bool is_behavior_loop, const std::vector<BehaviorExceptionType> &triggered_exception_types)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In edge-following or undefined mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In edge-following or undefined mode");

        bool is_beacon_valid = false; // Default: beacon invalid
        ProcessBeaconDetection(mark_loc_result, fusion_result, enter_multi_region_exploration_, is_beacon_valid);

        if (!enter_multi_region_exploration_)
        {
            ProcessSingleAreaExplorationMode(qrcode_loc_result, enter_multi_region_exploration_);
        }
        else
        {
            ProcessMultiAreaExplorationMode(mark_loc_result, fusion_result, enter_multi_region_exploration_, is_beacon_valid);
        }

        break;
    }

    case ThreadControl::CROSS_REGION_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In cross-region mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In cross-region mode");

        // Handle different cross-region states
        HandleExploreCrossRegionStates(cross_region_state);

        break;
    }

    case ThreadControl::RECHARGE_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In recharge mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In recharge mode");

        ProcessingExplorationRecharge(qrcode_loc_result);

        if (is_first_region_explore_mode_end_)
        {
            PublishVelocity(0.0, 0.0, 1000);
            is_first_region_explore_mode_end_ = false;
        }
        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In Behavior mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In Behavior mode");

        CheckVelocityAndUpdateState(behavior_state, is_behavior_loop, triggered_exception_types);

        break;
    }

    default:
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In other function mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In other function mode");

        break;
    }
}

/**
 * @brief Continuously detect beacon state
 *
 * @param mark_loc_result
 * @param qrcode_loc_result
 * @param is_beacon_valid
 */
void NavigationMowerAlg::ProcessBeaconDetection(MarkLocationResult &mark_loc_result,
                                                const PerceptionFusionResult &fusion_result,
                                                bool &enter_multi_region_exploration,
                                                bool &is_beacon_valid)
{
    if (!is_enable_unstake_mode_ || is_unstake_success_) /* Unstake mode not enabled or already successful (first stage complete) */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection1] Start region exploration mode");

        // First check for bottom obstacles before processing beacon detection
        const auto &occupancy_grid = fusion_result.occupancy_grid;
        if (!occupancy_grid.grid.empty() && occupancy_grid.width > 0 && occupancy_grid.height > 0)
        {
            bool has_bottom_obstacle_original = ObstacleClassification::HasBottomObstacle(occupancy_grid.grid, occupancy_grid.height, occupancy_grid.width, 0.01f);
            bool has_bottom_beacon_obstacle = ObstacleClassification::HasBottomBeaconObstacle(occupancy_grid.grid, occupancy_grid.height, occupancy_grid.width);
            bool has_bottom_obstacle = has_bottom_obstacle_original && has_bottom_beacon_obstacle;

            if (has_bottom_obstacle && CanTriggerBottomObstacleAvoidance())
            {
                LOG_INFO("[BeaconDetection1] Bottom obstacle detected (original: {}, beacon: {}), moving backward for fixed distance",
                         has_bottom_obstacle_original, has_bottom_beacon_obstacle);
                EdgeFollowDisable();
                // PublishVelocity(-mower_linear_, 0.0, 5.0 / mower_linear_); // Move backward for 0.5 seconds
                ControlLinearMotionWithIMUThread(0.5, 0.0, mower_linear_, -1);

                // Update the last avoidance time
                last_bottom_obstacle_avoidance_time_ = std::chrono::steady_clock::now();

                return;
            }
            else if (has_bottom_obstacle)
            {
                LOG_INFO_THROTTLE(1000, "[BeaconDetection1] Bottom obstacle detected but still in cooldown period, skipping avoidance");
            }
        }

        // 应用感知状态滤波器
        mark_loc_result.filtered_mark_perception_status = perception_status_filter_.UpdateAndFilter(mark_loc_result.mark_perception_status);

        if (mark_loc_result.filtered_mark_perception_status == 0) // 滤波后未检测到信标
        {
            LOG_INFO_THROTTLE(1000, "[BeaconDetection1] 1. Filtered perception did not detect beacon (raw: {}, filtered: {}), enable edge following",
                              mark_loc_result.mark_perception_status, mark_loc_result.filtered_mark_perception_status);

            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
        }
        else // 滤波后检测到信标
        {
            LOG_INFO("[BeaconDetection1] 2. Filtered perception detected beacon (raw: {}, filtered: {}, confidence: {:.2f})",
                     mark_loc_result.mark_perception_status, mark_loc_result.filtered_mark_perception_status,
                     perception_status_filter_.GetCurrentConfidence());

            if (mark_loc_result.mark_id_distance.size() <= 0) // No value in mark_id_distance
            {
                LOG_INFO("[BeaconDetection1] 2.1 No value in mark_id_distance from localization");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, is_cooldown_active_);
            }
            else // mark_id_distance has value
            {
                LOG_INFO("[BeaconDetection1] 2.2 mark_id_distance from localization has value");

                // Determine if beacon is valid (distance < 50cm is considered valid)
                int shortest_dis_inx = -1;
                std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
                FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

                if (shortest_dis_inx == -1) // If beacon invalid, do nothing, continue previous action
                {
                    LOG_INFO("[BeaconDetection1] 2.2.1 Cross-region beacon invalid");
                    HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, is_cooldown_active_);
                }
                else // If beacon valid, check stack container
                {
                    LOG_INFO("[BeaconDetection1] 2.2.2 Cross-region beacon valid");
                    LOG_INFO("[BeaconDetection1] 2.2.2 Valid beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    //! Beacon valid, can enter multi-region exploration
                    current_mark_id_ = mark_id_distance_vec[shortest_dis_inx].mark_id;
                    if (first_detection_beacon_)
                    {
                        beacon_status_ = BeaconStatus(current_mark_id_, 1);
                        first_detection_beacon_ = false;
                    }

                    enter_multi_region_exploration = true;
                    is_beacon_valid = true;
                }
            }
        }
    }
}
/**
 * @brief Use charging station QR code detection to determine when to stop edge following and switch to recharge
 *
 * @param qrcode_loc_result
 * @param enter_multi_region_exploration
 */
void NavigationMowerAlg::ProcessSingleAreaExplorationMode(const QRCodeLocationResult &qrcode_loc_result,
                                                          const bool &enter_multi_region_exploration)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        !enter_multi_region_exploration)                     /* Not entering multi-region exploration */
    {
        // LOG_INFO("[BeaconDetection1] Start single area exploration mode");
        LOG_INFO("[BeaconDetection1] Start single area exploration mode");

        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            LOG_INFO("[BeaconDetection1] QR code pose detected");

            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < recharge_distance_threshold_)
            {
                LOG_INFO("[BeaconDetection1] QR code pose within recharge distance threshold");

                auto current_time = std::chrono::steady_clock::now();
                qr_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_qr_explore_detection_time_);

                LOG_INFO("[BeaconDetection1] Charging station QR code detection cooldown timer (seconds): ({})", qr_detection_duration_.count());
                if (qr_detection_duration_.count() > qr_detection_cooldown_time_threshold_) // Increase timer
                {
                    qr_code_detection_count_++;
                    last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
                    LOG_INFO("[BeaconDetection1] Detected valid charging station QR code pose, current detection count: {}", qr_code_detection_count_);
                }
            }
            else
            {
                LOG_INFO("[BeaconDetection1] QR code pose outside recharge distance threshold");
            }
        }
        else
        {
            LOG_INFO("[BeaconDetection1] QR code pose not detected");
        }

        // Use QR code detection to determine when to stop edge following and switch to recharge
        if (qr_code_detection_count_ >= 2)
        {
            LOG_INFO("[BeaconDetection1] Detected valid charging station QR code pose twice, switching to recharge mode");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            is_single_area_recharge_ = true;

            // Reset state
            qr_code_detection_count_ = 1;

            // float area = 0.0;
            // float perimeter = 0.0;
            // if (area_calc_stop_callback_)
            // {
            //     area_calc_stop_callback_(GetSteadyClockTimestampMs(), area, perimeter);
            // }

            // LOG_INFO("[BeaconDetection1] area: {} , perimeter: {}", area, perimeter);

            // {
            //     master_region_explore_result_.is_exist = true;
            //     master_region_explore_result_.area = area;
            //     master_region_explore_result_.perimeter = perimeter;
            //     master_region_explore_result_.charge_station_flag = true;
            //     master_region_explore_result_.beacon_id = -1;

            //     slave_region_explore_result_.is_exist = false;
            // }
        }
    }
}

void NavigationMowerAlg::ProcessMultiAreaExplorationMode(const MarkLocationResult &mark_loc_result,
                                                         const PerceptionFusionResult &fusion_result,
                                                         const bool &enter_multi_region_exploration,
                                                         bool &is_beacon_valid)
{
    (void)mark_loc_result;
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        enter_multi_region_exploration)                      /* Entering multi-region exploration */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] Start multi-region exploration mode");

        if (is_beacon_valid) // Beacon is valid
        {
            // Reset cooldown timestamp and activate cooldown mechanism
            last_cooldown_time_ = std::chrono::steady_clock::now();
            is_cooldown_active_ = true;
            // ResetAndActivateCooldown();

            LOG_INFO("[BeaconDetection1] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
            LOG_INFO("[BeaconDetection1] Current detected mark_id: {}", current_mark_id_);

            // Initialize only on first entry
            if (is_first_enter_last_mark_detection_time_)
            {
                last_mark_detection_time_ = std::chrono::steady_clock::now(); // Beacon detection start timing
                is_first_enter_last_mark_detection_time_ = false;
                LOG_INFO("[BeaconDetection1] Beacon detection start timing");

                // stop
                float area = 0.0;
                float perimeter = 0.0;
                if (area_calc_stop_callback_)
                {
                    area_calc_stop_callback_(GetSteadyClockTimestampMs(), area, perimeter);
                }

                {
                    master_region_explore_result_.is_exist = false;
                    slave_region_explore_result_.is_exist = false;
                }

                // start
                if (area_calc_start_callback_)
                {
                    area_calc_start_callback_(GetSteadyClockTimestampMs());
                }
            }

            auto current_time = std::chrono::steady_clock::now();
            mark_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_mark_detection_time_);

            auto current_time_sec = std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
            auto last_mark_detection_time_sec = std::chrono::duration_cast<std::chrono::seconds>(last_mark_detection_time_.time_since_epoch());
            LOG_WARN("[BeaconDetection1] Current timestamp (seconds) current_time_sec({})", current_time_sec.count());
            LOG_WARN("[BeaconDetection1] Last timestamp (seconds) last_mark_detection_time_sec ({})", last_mark_detection_time_sec.count());
            LOG_INFO("[BeaconDetection1] Beacon detection cooldown timer (seconds): ({})", mark_detection_duration_.count());
            if (mark_detection_duration_.count() > mark_detection_cooldown_time_threshold_)
            {
                if (current_mark_id_ == beacon_status_.mark_id) // Same mark_id
                {
                    beacon_status_.beacon_look_count++;
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection1] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection1] Same mark_id, current detected mark_id: {}", current_mark_id_);
                }
                else // Different mark_id
                {
                    beacon_status_ = BeaconStatus(current_mark_id_, 1);
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection1] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection1] Different mark_id, current detected mark_id: {}", current_mark_id_);
                    LOG_WARN("[BeaconDetection1] Different mark_id, last detected mark_id: {}", beacon_status_.mark_id);
                }
            }

            if (beacon_status_.beacon_look_count >= 2)
            {
                // Start cross-region process
                LOG_INFO("[BeaconDetection1] Beacon detected more than twice, starting cross-region process");

                thread_control_ = ThreadControl::CROSS_REGION_THREAD;
                UpdateFeatureSelection(thread_control_);
                EdgeFollowDisable();
                is_single_area_recharge_ = false;

                // Reset state
                next_paired_beacon_id_ = PairNumber(current_mark_id_);
                beacon_status_ = BeaconStatus(next_paired_beacon_id_, 1);
                LOG_INFO("[BeaconDetection1] Next paired beacon id is {}", next_paired_beacon_id_);

                float area = 0.0;
                float perimeter = 0.0;
                if (area_calc_stop_callback_)
                {
                    area_calc_stop_callback_(GetSteadyClockTimestampMs(), area, perimeter);
                }

                LOG_INFO("[BeaconDetection1] area: {} , perimeter: {}", area, perimeter);

                if (is_master_region_) // Master region
                {
                    {
                        master_region_explore_result_.is_exist = true;
                        master_region_explore_result_.area = area;
                        master_region_explore_result_.perimeter = perimeter;
                        master_region_explore_result_.charge_station_flag = true;
                        master_region_explore_result_.beacon_id = current_mark_id_;
                    }

                    is_master_region_ = false; // Next is slave region
                }
                else // Slave region
                {
                    {
                        slave_region_explore_result_.is_exist = true;
                        slave_region_explore_result_.area = area;
                        slave_region_explore_result_.perimeter = perimeter;
                        slave_region_explore_result_.charge_station_flag = false;
                        slave_region_explore_result_.beacon_id = current_mark_id_;
                    }
                }
            }
            else
            {
                // Continue edge following
                LOG_INFO_THROTTLE(2000, "[BeaconDetection1] Beacon detection not more than twice, continue");

                // thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
                // UpdateFeatureSelection(thread_control_);

                HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, is_cooldown_active_);
                CrossRegionDisable();
            }
        }
    }
}

/**
 * @brief Pair a positive integer
 *
 * Rule:
 * If the input positive integer n is odd, return n+1;
 * If n is even, return n-1.
 * Formula:
 * If n % 2 == 1, output n+1
 * If n % 2 == 0, output n-1
 *
 * @param n positive integer
 * @return int paired value
 */
int NavigationMowerAlg::PairNumber(int n)
{
    // Check if n is odd
    if (n % 2 == 1)
    {
        // If n is odd, return n+1
        return n + 1;
    }
    else
    {
        // If n is even, return n-1
        return n - 1;
    }
}

void NavigationMowerAlg::ProcessingExplorationRecharge(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_single_area_recharge_) // Single area recharge, report based on recharge condition
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE) // Can calculate charging station QR code pose
        {
            LOG_INFO("[BeaconDetection1] Single area recharge, QR code pose detected");

            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < recharge_distance_threshold_)
            {
                LOG_INFO("[BeaconDetection1] Single area recharge, QR code pose within recharge distance threshold");

                //! 1. Get single area info
                float area = 0.0;
                float perimeter = 0.0;
                if (area_calc_stop_callback_)
                {
                    area_calc_stop_callback_(GetSteadyClockTimestampMs(), area, perimeter);
                }

                LOG_INFO("[BeaconDetection1] area: {} , perimeter: {}", area, perimeter);

                {
                    master_region_explore_result_.is_exist = true;
                    master_region_explore_result_.area = area;
                    master_region_explore_result_.perimeter = perimeter;
                    master_region_explore_result_.charge_station_flag = true;
                    master_region_explore_result_.beacon_id = -1;

                    slave_region_explore_result_.is_exist = false;
                }

                //! 2. Report region exploration result
                RegionExploreResult region_explore_result;
                {
                    region_explore_result.result = true;
                    region_explore_result.timestamp = GetSteadyClockTimestampMs();
                    region_explore_result.master_region_map_result = master_region_explore_result_;
                    region_explore_result.slave_region_map_result = slave_region_explore_result_;
                }

                if (region_explore_result_callback_)
                {
                    region_explore_result_callback_(region_explore_result);
                }

                is_region_explore_mode_start_ = false;
            }
            else
            {
                LOG_INFO("[BeaconDetection1] Single area recharge, QR code pose outside recharge distance threshold");
            }
        }
        else
        {
            LOG_INFO("[BeaconDetection1] Single area recharge, QR code pose not detected");
        }
    }
    else // Multi-region recharge, report directly
    {
        RegionExploreResult region_explore_result;
        {
            region_explore_result.result = true;
            region_explore_result.timestamp = GetSteadyClockTimestampMs();
            region_explore_result.master_region_map_result = master_region_explore_result_;
            region_explore_result.slave_region_map_result = slave_region_explore_result_;
        }

        if (region_explore_result_callback_)
        {
            region_explore_result_callback_(region_explore_result);
        }

        is_region_explore_mode_start_ = false;
    }
}

void NavigationMowerAlg::ResetAndActivateCooldown()
{
    // Reset cooldown timestamp and activate cooldown mechanism
    last_cooldown_time_ = std::chrono::steady_clock::now();
    is_cooldown_active_ = true;
}

void NavigationMowerAlg::HandleEdgePerceptionBeaconDetection(const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result, bool &is_cooldown_active)
{
    if (is_cooldown_active) // Cooldown mechanism active
    {
        LOG_DEBUG("[BeaconDetection1] Cooldown mechanism activated");

        auto current_time = std::chrono::steady_clock::now();
        edge_perception_drive_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_cooldown_time_);

        // Print time difference
        LOG_DEBUG("[BeaconDetection1] Edge perception drive cooldown timer (seconds): ({})", edge_perception_drive_duration_.count());
        HandleEdgeCooldownMechanism(mark_loc_result, fusion_result, is_cooldown_active, edge_perception_drive_duration_, edge_perception_drive_cooldown_time_threshold_);
    }
    else // Cooldown mechanism not active
    {
        LOG_DEBUG("[BeaconDetection1] Cooldown mechanism not activated");

        LOG_DEBUG("[BeaconDetection1] Enable perception drive, disable edge following");

        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);

        PerceptionBasedAdjustment(mark_loc_result, fusion_result);
    }
}

void NavigationMowerAlg::HandleEdgeCooldownMechanism(const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result, bool &is_cooldown_active,
                                                     std::chrono::seconds &perception_drive_duration, int &perception_drive_cooldown_time_threshold) // Perception detection result
{
    if (perception_drive_duration.count() >= perception_drive_cooldown_time_threshold)
    {
        is_cooldown_active = false;
        LOG_INFO("[BeaconDetection1] Timer exceeded ({}s), cooldown ended", perception_drive_cooldown_time_threshold);

        LOG_INFO("[BeaconDetection1] Enable perception drive, disable edge following");

        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);

        PerceptionBasedAdjustment(mark_loc_result, fusion_result);
    }
    else
    {
        // Cooldown not finished, skip execution
        LOG_INFO("[BeaconDetection1] Cooldown not finished, not exceeding ({}s)", perception_drive_cooldown_time_threshold);

        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
        LOG_INFO("[BeaconDetection1] Enable edge following");
    }
}

void NavigationMowerAlg::ProcessExplorationUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot is powered */
        mcu_triggers_region_exploration_)                  /* MCU exploration request */
    {
        LOG_INFO("[ProcessExplorationUnstakeMode] Exploration mode. Start unstake mode");
        PerformUnstakeMode(); // Execute unstake operation
    }
}

void NavigationMowerAlg::ProcessExplorationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot is powered */
        mcu_triggers_region_exploration_)                  /* MCU exploration request */
    {
        LOG_INFO("[ProcessExplorationUnstakeMode] Exploration mode. Start unstake mode");

        PerformUnstakeMode(qrcode_loc_result); // Execute unstake operation
    }
}

void NavigationMowerAlg::HandleExplorationMcuException(const ExceptionInfo &exception_info)
{
    const auto &recharge_state = exception_info.recharge_state;
    const auto &cross_region_state = exception_info.cross_region_state;
    const auto &is_slipping = exception_info.is_slipping;
    const auto &mcu_exception_status = exception_info.mcu_exception_status;
    const auto &is_stuck = exception_info.is_stuck;
    // If charging station QR code detected
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        (recharge_state == RechargeRunningState::ADJUST_TO_STATION ||
         recharge_state == RechargeRunningState::ACCURATE_DOCK)) // Charging station QR code found /**Cannot perform recovery mode */
    {
        ProcessExplorationRechargeException(recharge_state);
    }
    // If beacon QR code detected (cross-region case)
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON &&
             cross_region_state != CrossRegionRunningState::UNDEFINED &&
             cross_region_state != CrossRegionRunningState::FINISH) // Beacon found /**Cannot perform recovery mode */
    {
        ProcessExplorationCrossRegionException(cross_region_state);
    }
    // Other exceptions, enter recovery mode
    else
    {
        ProcessRecoveryException(is_slipping, mcu_exception_status, is_stuck);
    }
}

// Handle MCU exception for recharge request logic
void NavigationMowerAlg::ProcessExplorationRechargeException(RechargeRunningState recharge_state)
{
    (void)recharge_state;
    LOG_WARN_THROTTLE(500, "[BeaconDetection1] Charging station QR code detected");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_region_exploration_)                    /* MCU exploration request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection1] Switch to recharge mode");

        // Switch to recharge thread and update feature selection
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle MCU exception for cross-region request logic
void NavigationMowerAlg::ProcessExplorationCrossRegionException(CrossRegionRunningState cross_region_state)
{
    (void)cross_region_state;
    LOG_WARN_THROTTLE(500, "[BeaconDetection1] Beacon QR code detected");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_region_exploration_)                    /* MCU exploration request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection1] Switch to cross-region mode");

        // Switch to cross-region thread and update feature selection
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

void NavigationMowerAlg::ShowExplorePrint(BehaviorRunningState &behavior_state)
{
    (void)behavior_state;
    LOG_INFO_THROTTLE(1500, "[BeaconDetection1] Region exploration count: ({})", region_count_);
}

//==============================================================================
// Exception handling functions
//==============================================================================
void NavigationMowerAlg::HandleMcuException(const ExceptionInfo &exception_info)
{
    const auto &recharge_state = exception_info.recharge_state;
    const auto &cross_region_state = exception_info.cross_region_state;
    const auto &is_slipping = exception_info.is_slipping;
    const auto &mcu_exception_status = exception_info.mcu_exception_status;
    const auto &is_stuck = exception_info.is_stuck;
    // If charging station QR code detected
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        (recharge_state == RechargeRunningState::ADJUST_TO_STATION ||
         recharge_state == RechargeRunningState::ACCURATE_DOCK)) // Charging station QR code found /**Cannot perform recovery mode */
    {
        ProcessRechargeException(recharge_state);
    }
    // If beacon QR code detected (cross-region case)
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON && // Perception detects beacon
             cross_region_state != CrossRegionRunningState::UNDEFINED &&
             cross_region_state != CrossRegionRunningState::FINISH) // Beacon found state /**Cannot perform recovery mode */
    {
        ProcessCrossRegionException(cross_region_state);
    }
    else
    {
        LOG_INFO("handle exception is_slipping: {}, mcu_exception_status: {}", is_slipping, static_cast<int>(mcu_exception_status));
        ProcessRecoveryException(is_slipping, mcu_exception_status, is_stuck);
    }
}

// Handle MCU exception for recharge request logic
void NavigationMowerAlg::ProcessRechargeException(RechargeRunningState recharge_state)
{
    (void)recharge_state;
    LOG_WARN_THROTTLE(500, "[BeaconDetection1] Charging station QR code detected");

    // MCU publishes recharge request
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_recharge_ &&                            /* MCU recharge request */
        !mcu_triggers_cross_region_ &&                       /* MCU no cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */

    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection1] Switch to recharge mode");

        // Switch to recharge thread and update feature selection
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle MCU exception for cross-region request logic
void NavigationMowerAlg::ProcessCrossRegionException(CrossRegionRunningState cross_region_state)
{
    (void)cross_region_state;
    LOG_WARN_THROTTLE(500, "[BeaconDetection1] Beacon QR code detected");

    // MCU publishes cross-region request
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        !mcu_triggers_recharge_ &&                           /* MCU no recharge request */
        mcu_triggers_cross_region_ &&                        /* MCU cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection1] Switch to cross-region mode");

        // Switch to cross-region thread and update feature selection
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle other exceptions, enter recovery mode
void NavigationMowerAlg::ProcessRecoveryException(bool is_slipping, McuExceptionStatus mcu_exception_status, bool is_stuck)
{
    LOG_WARN_THROTTLE(500, "[BeaconDetection1] Handle collision or lifting exception");

    if ((!is_enable_unstake_mode_ || is_unstake_success_)) /* Unstake mode not enabled or already successful (first stage complete) */
    {
        thread_control_ = ThreadControl::BEHAVIOR_THREAD;
        std::vector<BehaviorExceptionType> behavior_exception_types;
        if (is_stuck)
        {
            LOG_INFO("add stuck exception");
            behavior_exception_types.push_back(BehaviorExceptionType::STUCK);
        }
        if (is_slipping)
        {
            LOG_INFO("add slip exception");
            behavior_exception_types.push_back(BehaviorExceptionType::SLIP);
        }
        if (mcu_exception_status == McuExceptionStatus::COLLISION)
        {
            LOG_INFO("add collision exception");
            behavior_exception_types.push_back(BehaviorExceptionType::COLLISION);
        }
        if (mcu_exception_status == McuExceptionStatus::LIFTING)
        {
            LOG_INFO("add lifting exception");
            behavior_exception_types.push_back(BehaviorExceptionType::LIFTING);
        }
        UpdateFeatureSelection(thread_control_, behavior_exception_types);
    }
}

//==============================================================================
// Normal state handling functions
//==============================================================================

// Handle different stages in normal state
void NavigationMowerAlg::HandleNormalOperation(CrossRegionRunningState cross_region_state, RechargeRunningState recharge_state,
                                               BehaviorRunningState &behavior_state, const QRCodeLocationResult &qrcode_loc_result,
                                               const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result,
                                               const ChargeStationDetectResult &station_result,
                                               bool is_behavior_loop, const std::vector<BehaviorExceptionType> &triggered_exception_types)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    case ThreadControl::CROSS_REGION_THREAD:
    case ThreadControl::RECHARGE_THREAD:
    case ThreadControl::RANDOM_MOWING_THREAD:
    case ThreadControl::SPIRAL_MOWING_THREAD:
    {
        ProcessRandomMowing();
        ProcessSpiralMowing();
        ProcessCrossRegionMode(cross_region_state);
        ProcessRechargeMode(recharge_state, cross_region_state, qrcode_loc_result,
                            mark_loc_result, fusion_result, station_result);

        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In Behavior mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In Behavior mode");

        CheckVelocityAndUpdateState(behavior_state, is_behavior_loop, triggered_exception_types);

        break;
    }

    default:
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In other function mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In other function mode");

        break;
    }
}

void NavigationMowerAlg::CheckVelocityAndUpdateState(BehaviorRunningState &behavior_state, bool is_behavior_loop, const std::vector<BehaviorExceptionType> &triggered_exception_types)
{
    // Get current linear and angular velocity from motor speed
    MotorSpeedData motor_speed_data;
    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        motor_speed_data = motor_speed_data_;
    }

    float v_left = motor_speed_data.motor_speed_left * 2 * M_PI / 60.0f * wheel_radius_;   // Left wheel speed (m/s)
    float v_right = motor_speed_data.motor_speed_right * 2 * M_PI / 60.0f * wheel_radius_; // Right wheel speed (m/s)
    float act_linear = (v_right + v_left) / 2.0f;                                          // Actual linear velocity (m/s)
    float act_angular = (v_right - v_left) / wheel_base_;                                  // Actual angular velocity (rad/s)

    // Check if velocity is close to zero
    if (std::abs(act_linear) < linear_velocity_threshold_ &&
        std::abs(act_angular) < angular_velocity_threshold_)
    {
        if (!is_velocity_zero_)
        {
            // Velocity is close to zero for the first time, start timing
            last_zero_velocity_time_ = std::chrono::steady_clock::now();
            is_velocity_zero_ = true;
            LOG_INFO("[BeaconDetection1] Velocity is close to zero, start timing");
        }
        else
        {
            // Velocity remains close to zero, check duration
            auto current_time = std::chrono::steady_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::seconds>(
                current_time - last_zero_velocity_time_);

            if (duration.count() >= ZERO_VELOCITY_THRESHOLD_SECONDS)
            {
                // Velocity close to zero for 1 second, end recovery mode
                LOG_ERROR("[BeaconDetection1] Velocity close to zero for 1 second, ending recovery mode");
                thread_control_ = ThreadControl::UNDEFINED;
                UpdateFeatureSelection(thread_control_);
                is_velocity_zero_ = false; // Reset flag

                // HandleRecoveryEnd(); // Notify suspended unstake tasks to continue
                // SetSlippingStatus(false);
            }
        }
    }
    else
    {
        // Velocity exceeds threshold, reset timer
        if (is_velocity_zero_)
        {
            LOG_INFO("[BeaconDetection1] Velocity is no longer close to zero, reset timer");
            is_velocity_zero_ = false;
        }
    }

    if (behavior_state == BehaviorRunningState::SUCCESS)
    {
        LOG_ERROR("[BeaconDetection1] Behavior state SUCCESS, ending recovery mode is_behavior_loop: {}", is_behavior_loop);
        is_recover_from_exception_ = true;
        is_behavior_loop_ = is_behavior_loop;
        last_triggered_exception_types_ = triggered_exception_types;
        thread_control_ = ThreadControl::UNDEFINED;
        UpdateFeatureSelection(thread_control_);
        is_velocity_zero_ = false; // Reset flag

        // HandleRecoveryEnd(); // Notify suspended unstake tasks to continue
        // SetSlippingStatus(false);
    }
}

// Stage 1: Unstake handling
void NavigationMowerAlg::ProcessNormalOperationUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot is powered */
        (mcu_triggers_recharge_ ||                         /* MCU recharge request */
         mcu_triggers_cross_region_ ||                     /* MCU cross-region request */
         mcu_triggers_mower_ ||                            /* MCU random mowing request */
         mcu_triggers_spiral_mower_))                      /* MCU spiral mowing request */

    {
        LOG_INFO("[ProcessNormalOperationUnstakeMode1] Powered, start unstake task...");
        PerformUnstakeMode(); // Execute unstake operation
    }
}

// Stage 1: Unstake handling (QR code localization version)
void NavigationMowerAlg::ProcessNormalOperationUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot is powered */
        (mcu_triggers_recharge_ ||                         /* MCU recharge request */
         mcu_triggers_cross_region_ ||                     /* MCU cross-region request */
         mcu_triggers_mower_ ||                            /* MCU random mowing request */
         mcu_triggers_spiral_mower_))                      /* MCU spiral mowing request */

    {
        LOG_INFO("[ProcessNormalOperationUnstakeMode1] Powered, start unstake task...");

        is_unstaking_ = true;

        PerformUnstakeMode(qrcode_loc_result); // Execute unstake operation
        // PerformUnstakeModeAsync(qrcode_loc_result);
    }
}

// Stage 1: Pre-mowing processing
void NavigationMowerAlg::PreProcessingMowing(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        !is_power_connected_ &&                            /* Robot not powered */
        (mcu_triggers_recharge_ ||                         /* MCU recharge request */
         mcu_triggers_cross_region_ ||                     /* MCU cross-region request */
         mcu_triggers_mower_ ||                            /* MCU random mowing request */
         mcu_triggers_spiral_mower_))                      /* MCU spiral mowing request */

    {
        LOG_INFO("[PreProcessingMowing] Not powered, start pre-mowing logic");

        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE &&                                    // Can calculate charging station QR code pose
            sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) <= mower_start_qr_distance_threshold_) // 0.8
        {
            float mower_start_qr_distance = sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2));

            // ControlLinearMotion(unstake_distance_ - mower_start_qr_distance, 0.0, unstake_vel_linear_, -1); // Exit charging station
            // ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);                            // Rotate 45 degrees away from charging station

            ControlLinearMotionWithIMUThread(unstake_distance_ - mower_start_qr_distance, 0.0, unstake_vel_linear_, -1);
            ControlRotaryMotionWithIMU(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);
            PublishVelocity(0.0, 0.0, 1000); // Stop for 1s

            if (is_on_grass_field_) // On grass
            {
                is_unstake_mode_completed_ = true; // Unstake completed
                is_unstake_success_ = true;        // Unstake successful

                SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report success
                LOG_INFO("[BeaconDetection1] On grass. Unstake mode succeeded, start mowing mode");
            }
            else // Not on grass
            {
                is_unstake_mode_completed_ = true; // Unstake completed
                is_unstake_success_ = false;       // Unstake failed

                SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report failure
                LOG_ERROR("[BeaconDetection1] Not on grass. Unstake mode failed, report error");

                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
            }
        }
        else
        {
            is_unstake_mode_completed_ = true;
            is_unstake_success_ = true;
            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_);
        }
    }
}

// Stage 2: Random mowing handling
void NavigationMowerAlg::ProcessRandomMowing()
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled || unstake successful (stage 1 completed) */
        !mcu_triggers_recharge_ &&                           /* MCU no recharge request */
        !mcu_triggers_cross_region_ &&                       /* MCU no cross-region request */
        mcu_triggers_mower_ &&                               /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */
    {
        // Check non-grass state during mowing
        if (!is_on_grass_field_ && non_grass_duration_.count() >= NON_GRASS_WARN_THRESHOLD_SECONDS) // 5s
        {
            LOG_WARN("[BeaconDetection1] Non-grass detected for more than {} seconds in random mowing mode, report WARN exception", NON_GRASS_WARN_THRESHOLD_SECONDS);
            PublishVelocity(0.0, mower_angular_);
            PublishException(SocExceptionLevel::WARNING, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION);

            TriggerExceptionPublishing();

            if (non_grass_duration_.count() >= NON_GRASS_ERROR_THRESHOLD_SECONDS)
            {
                LOG_ERROR("[BeaconDetection1] Non-grass detected for more than {} seconds in random mowing mode, report ERROR exception", NON_GRASS_ERROR_THRESHOLD_SECONDS);
                SetAllTaskClose();
                PublishVelocity(0.0, 0.0, 1000);
                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION);
            }
        }
        else
        {
            // Add in Run() or state machine loop
            if (is_publishing_exception_)
            {
                auto current_time = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - exception_start_time_);

                if (duration.count() < 1000)
                {
                    PublishException(SocExceptionLevel::NONE, SocExceptionValue::NO_EXCEPTION);
                }
                else
                {
                    is_publishing_exception_ = false; // Stop publishing
                }
            }

            LOG_INFO_THROTTLE(500, "[BeaconDetection1] Start random mowing mode");
            PerformRandomMowing(); // Execute random mowing operation
        }
    }
}

// Stage 3: Spiral mowing handling
void NavigationMowerAlg::ProcessSpiralMowing()
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled || unstake successful (stage 1 completed) */
        !mcu_triggers_recharge_ &&                           /* MCU no recharge request */
        !mcu_triggers_cross_region_ &&                       /* MCU no cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        mcu_triggers_spiral_mower_)                          /* MCU spiral mowing request */
    {
        // Check non-grass state during mowing
        if (!is_on_grass_field_ && non_grass_duration_.count() >= NON_GRASS_WARN_THRESHOLD_SECONDS)
        {
            LOG_WARN("[BeaconDetection1] Non-grass detected for more than {} seconds in spiral mowing mode, report WARN exception", NON_GRASS_WARN_THRESHOLD_SECONDS);
            PublishVelocity(0.0, mower_angular_);
            PublishException(SocExceptionLevel::WARNING, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_5_TO_30S_EXCEPTION);

            TriggerExceptionPublishing();

            if (non_grass_duration_.count() >= NON_GRASS_ERROR_THRESHOLD_SECONDS)
            {
                LOG_ERROR("[BeaconDetection1] Non-grass detected for more than {} seconds in spiral mowing mode, report ERROR exception", NON_GRASS_ERROR_THRESHOLD_SECONDS);
                SetAllTaskClose();
                PublishVelocity(0.0, 0.0, 1000);
                PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_MOWING_NON_GRASSLAND_OVER_30S_EXCEPTION);
            }
        }
        else
        {
            // Add in Run() or state machine loop
            if (is_publishing_exception_)
            {
                auto current_time = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(current_time - exception_start_time_);

                if (duration.count() < 1000)
                {
                    PublishException(SocExceptionLevel::NONE, SocExceptionValue::NO_EXCEPTION);
                }
                else
                {
                    is_publishing_exception_ = false; // Stop publishing
                }
            }

            LOG_INFO_THROTTLE(500, "[BeaconDetection1] Start spiral mowing mode");
            PerformSpiralMowing(); // Execute spiral mowing operation
        }
    }
}

// Stage 4: Cross-region handling
void NavigationMowerAlg::ProcessCrossRegionMode(CrossRegionRunningState cross_region_state)
{
    (void)cross_region_state;
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled || unstake successful (stage 1 completed) */
        !mcu_triggers_recharge_ &&                           /* MCU no recharge request */
        mcu_triggers_cross_region_ &&                        /* MCU cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection1] Start cross-region mode");

        // Switch to cross-region thread and update feature selection
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Stage 5: Recharge handling
void NavigationMowerAlg::ProcessRechargeMode(RechargeRunningState recharge_state, CrossRegionRunningState cross_region_state,
                                             const QRCodeLocationResult &qrcode_loc_result, const MarkLocationResult &mark_loc_result,
                                             const PerceptionFusionResult &fusion_result,
                                             const ChargeStationDetectResult &station_result)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled || unstake successful (stage 1 completed) */
        mcu_triggers_recharge_ &&                            /* MCU recharge request */
        !mcu_triggers_cross_region_ &&                       /* MCU no cross-region request */
        !mcu_triggers_mower_ &&                              /* MCU random mowing request */
        !mcu_triggers_spiral_mower_)                         /* MCU spiral mowing request */
    {
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] Start recharge mode");

        // Consider whether to support cross-region recharge
        if (recharge_edge_follow_ && recharge_state == RechargeRunningState::BACK_GROUND_RUN)
        {
            PerformOtherRecharge(mark_loc_result, cross_region_state, qrcode_loc_result, fusion_result, station_result);
        }
        else
        {
            if (recharge_state == RechargeRunningState::RECHARGE_FINISH)
            {
                mcu_triggers_recharge_ = false;
            }
            if (recharge_state != RechargeRunningState::BACK_GROUND_RUN)
            {
                // reset cross recharge parameters
                cross_recharge_mark_id_ = -1;
                cross_recharge_beacon_status_ = BeaconStatus(-1, 0);
                first_recharge_cross_region_ = true;
                cross_recharge_detection_beacon_ = true;
                recharge_edge_follow_ = true;
                EdgeFollowDisable();
            }
            // Switch to recharge thread and update feature selection
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            UpdateFeatureSelection(thread_control_);
        }
    }
}

void NavigationMowerAlg::PerformOtherRecharge(const MarkLocationResult &mark_loc_result,
                                              CrossRegionRunningState cross_region_state,
                                              const QRCodeLocationResult &qrcode_loc_result,
                                              const PerceptionFusionResult &fusion_result,
                                              const ChargeStationDetectResult &station_result)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::RECHARGE_THREAD:
    {
        LOG_INFO_THROTTLE(1000, "RECHARGE: In edge-follow mode");
        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
        break;
    }

    case ThreadControl::CROSS_REGION_THREAD:
    {
        LOG_INFO_THROTTLE(1000, "RECHARGE: In cross-region mode");
        HandleRechargeCrossRegionStates(cross_region_state);
        break;
    }

    case ThreadControl::PERCEPTION_EDGE_THREAD:
    {
        LOG_INFO_THROTTLE(1000, "RECHARGE: the charging station head: {}", station_result.is_head);
        LOG_INFO_THROTTLE(1000, "RECHARGE: the charging station range: {}", station_result.range);
        if (station_result.is_chargestation && station_result.is_head && station_result.range == 0 &&
            qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE &&
            fabs(qrcode_loc_result.xyzrpw.x) <= recharge_distance_)
        {
            LOG_INFO_THROTTLE(1000, "RECHARGE: Find the charging station, start recharging");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            EdgeFollowDisable();
            if (edge_follow_status_callback_)
            {
                edge_follow_status_callback_(0);
            }
            // reset cross recharge parameters
            cross_recharge_mark_id_ = -1;
            cross_recharge_beacon_status_ = BeaconStatus(-1, 0);
            first_recharge_cross_region_ = true;
            cross_recharge_detection_beacon_ = true;
            recharge_edge_follow_ = false;
            break;
        }
        else
        {
            LOG_INFO_THROTTLE(1000, "RECHARGE: Not find the charging station, start edge follow");
            bool is_beacon_valid = false;
            RechargeBeaconDetection(mark_loc_result, fusion_result, is_beacon_valid);

            if (is_beacon_valid)
            {
                // Reset params
                last_cooldown_time_ = std::chrono::steady_clock::now();
                is_cooldown_active_ = true;
                if (first_recharge_cross_region_)
                {
                    last_recharge_cross_region_time_ = std::chrono::steady_clock::now();
                    first_recharge_cross_region_ = false;
                    LOG_INFO("Recharge Cross Region Start Timing");
                }
                auto current_time = std::chrono::steady_clock::now();
                recharge_cross_region_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_recharge_cross_region_time_);
                if (recharge_cross_region_duration_.count() > mark_detection_cooldown_time_threshold_)
                {
                    if (cross_recharge_mark_id_ == cross_recharge_beacon_status_.mark_id)
                    {
                        cross_recharge_beacon_status_.beacon_look_count++;
                        last_recharge_cross_region_time_ = std::chrono::steady_clock::now();
                    }
                    else
                    {
                        cross_recharge_beacon_status_ = BeaconStatus(cross_recharge_mark_id_, 1);
                        last_recharge_cross_region_time_ = std::chrono::steady_clock::now();
                    }
                }
                if (cross_recharge_mark_id_ == 2)
                {
                    LOG_INFO_THROTTLE(1000, "RECHARGE: Robot is in the auxiliary zone, Start cross region");
                    thread_control_ = ThreadControl::CROSS_REGION_THREAD;
                    UpdateFeatureSelection(thread_control_);
                    EdgeFollowDisable();
                    break;
                }
            }
        }
        LOG_INFO_THROTTLE(1000, "RECHARGE: Continue in edge follow mode");
        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
        break;
    }

    default:
        LOG_INFO_THROTTLE(1000, "RECHARGE: In other function mode");
        break;
    }
}

void NavigationMowerAlg::RechargeBeaconDetection(MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result, bool &is_beacon_valid)
{
    if (!is_enable_unstake_mode_ || is_unstake_success_)
    {
        LOG_INFO_THROTTLE(500, "Start Beacon Detection");

        // First check for bottom obstacles before processing beacon detection
        const auto &occupancy_grid = fusion_result.occupancy_grid;
        if (!occupancy_grid.grid.empty() && occupancy_grid.width > 0 && occupancy_grid.height > 0)
        {
            bool has_bottom_obstacle_original = ObstacleClassification::HasBottomObstacle(occupancy_grid.grid, occupancy_grid.height, occupancy_grid.width, 0.01f);
            bool has_bottom_beacon_obstacle = ObstacleClassification::HasBottomBeaconObstacle(occupancy_grid.grid, occupancy_grid.height, occupancy_grid.width);
            bool has_bottom_obstacle = has_bottom_obstacle_original && has_bottom_beacon_obstacle;

            if (has_bottom_obstacle && CanTriggerBottomObstacleAvoidance())
            {
                LOG_INFO("[RechargeBeaconDetection] Bottom obstacle detected (original: {}, beacon: {}), moving backward for fixed distance",
                         has_bottom_obstacle_original, has_bottom_beacon_obstacle);
                EdgeFollowDisable();
                // PublishVelocity(-mower_linear_, 0.0, 5.0 / mower_linear_); // Move backward for 0.5 seconds
                ControlLinearMotionWithIMUThread(0.5, 0.0, mower_linear_, -1);

                // Update the last avoidance time
                last_bottom_obstacle_avoidance_time_ = std::chrono::steady_clock::now();

                return;
            }
            else if (has_bottom_obstacle)
            {
                LOG_INFO_THROTTLE(1000, "[RechargeBeaconDetection] Bottom obstacle detected but still in cooldown period, skipping avoidance");
            }
        }

        // 应用感知状态滤波器
        mark_loc_result.filtered_mark_perception_status = perception_status_filter_.UpdateAndFilter(mark_loc_result.mark_perception_status);

        if (mark_loc_result.filtered_mark_perception_status == 0)
        {
            LOG_INFO_THROTTLE(1000, "Filtered perception did not detect beacon (raw: {}, filtered: {}), enable edge following",
                              mark_loc_result.mark_perception_status, mark_loc_result.filtered_mark_perception_status);
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
        }
        else // 滤波后检测到信标
        {
            LOG_INFO("Filtered perception detected beacon (raw: {}, filtered: {}, confidence: {:.2f})",
                     mark_loc_result.mark_perception_status, mark_loc_result.filtered_mark_perception_status,
                     perception_status_filter_.GetCurrentConfidence());
            if (mark_loc_result.mark_id_distance.size() <= 0)
            {
                LOG_INFO("No value in mark_id_distance from localization");
                HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, is_cooldown_active_);
            }
            else // mark_id_distance has value
            {
                LOG_INFO("mark_id_distance from localization has value");
                // Determine if beacon is valid (distance < 50cm is considered valid)
                int shortest_dis_inx = -1;
                std::vector<MarkIdDistance> mark_id_distance_vec = mark_loc_result.mark_id_distance;
                FingVaidBeaconIdx(mark_id_distance_vec, shortest_dis_inx);

                if (shortest_dis_inx == -1) // If beacon invalid, do nothing, continue previous action
                {
                    LOG_INFO("Cross-region beacon invalid");
                    HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, is_cooldown_active_);
                }
                else // If beacon valid, check stack container
                {
                    LOG_INFO("Cross-region beacon valid");
                    LOG_INFO("Valid beacon mark_id = {}", mark_id_distance_vec[shortest_dis_inx].mark_id);

                    cross_recharge_mark_id_ = mark_id_distance_vec[shortest_dis_inx].mark_id;
                    if (cross_recharge_detection_beacon_)
                    {
                        cross_recharge_beacon_status_ = BeaconStatus(cross_recharge_mark_id_, 1);
                        cross_recharge_detection_beacon_ = false;
                    }
                    is_beacon_valid = true;
                }
            }
        }
    }
}

void NavigationMowerAlg::HandleRechargeCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION)
    {
        LOG_INFO("From non-grass to grass, close cross-region, switch to edge following");
        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
        CrossRegionDisable();
        mcu_triggers_cross_region_ = false;
        UpdateCrossRegionRunningState(CrossRegionRunningState::FINISH);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION)
    {
        LOG_INFO("Beacon detection, close cross-region, switch to edge following");
        thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
        UpdateFeatureSelection(thread_control_);
        CrossRegionDisable();
        mcu_triggers_cross_region_ = false;
        UpdateCrossRegionRunningState(CrossRegionRunningState::FINISH);
    }
}

void NavigationMowerAlg::ResetMowerAlgFlags()
{
    // Runtime variables
    is_on_docker_ = false;
    mower_running_state_ = MowerRunningState::STOP;
    thread_control_ = ThreadControl::UNDEFINED; // Does not affect recharge function
    random_mower_state_ = RandomMowerRunningState::NORMAL;
    is_unstake_mode_completed_ = false;
    is_unstake_success_ = false;
    is_power_connected_ = false;

    mcu_triggers_cross_region_ = false;
    mcu_triggers_recharge_ = false;
    mcu_triggers_mower_ = false;
    mcu_triggers_spiral_mower_ = false;
    mcu_triggers_region_exploration_ = false;
    mcu_triggers_cut_border_ = false;

    is_region_explore_mode_start_ = false;
    is_cut_border_mode_start_ = false;

    mower_completed_ = false;
    frames_.clear();
    is_on_grass_field_ = false;

    // New
    is_cooldown_active_ = false;
    is_first_enter_last_cooldown_time_ = true;
    last_cooldown_time_ = std::chrono::steady_clock::now();
    edge_perception_drive_duration_ = std::chrono::seconds(0);

    enter_multi_region_exploration_ = false;

    // Single area
    qr_code_detection_count_ = 1;
    is_first_enter_explore_last_qr_detection_time_ = true;
    last_qr_explore_detection_time_ = std::chrono::steady_clock::now();
    qr_detection_duration_ = std::chrono::seconds(0);
    is_single_area_recharge_ = false;

    // Multi-area
    beacon_status_ = BeaconStatus(-1, 0);
    current_mark_id_ = -1;
    is_first_enter_last_mark_detection_time_ = true;
    last_mark_detection_time_ = std::chrono::steady_clock::now();
    mark_detection_duration_ = std::chrono::seconds(0);

    first_detection_beacon_ = true;
    next_paired_beacon_id_ = -1; // Next paired beacon id

    cross_recharge_mark_id_ = -1;
    cross_recharge_beacon_status_ = BeaconStatus(-1, 0);
    first_recharge_cross_region_ = true;
    cross_recharge_detection_beacon_ = true;
    recharge_edge_follow_ = true;

    region_count_ = 1; // Default 1 region

    // Region exploration info
    is_master_region_ = true;
    is_first_region_explore_mode_end_ = true;

    {
        master_region_explore_result_.is_exist = false;
        master_region_explore_result_.area = 0.0;
        master_region_explore_result_.perimeter = 0.0;
        master_region_explore_result_.charge_station_flag = false;
        master_region_explore_result_.beacon_id = -1;

        slave_region_explore_result_.is_exist = false;
        slave_region_explore_result_.area = 0.0;
        slave_region_explore_result_.perimeter = 0.0;
        slave_region_explore_result_.charge_station_flag = false;
        slave_region_explore_result_.beacon_id = -1;
    }

    /** Mowing non-grass exception detection */
    is_first_non_grass_detection_ = true;
    last_grass_time_ = std::chrono::steady_clock::now();
    non_grass_duration_ = std::chrono::seconds(0);

    exception_start_time_ = std::chrono::steady_clock::now();
    is_publishing_exception_ = false;

    /** Self-check mode */
    is_self_checking_ = false;                                 // Whether self-check is in progress
    self_check_start_time_ = std::chrono::steady_clock::now(); // Self-check start time
    is_self_success_ = false;
    is_self_recovery_active_ = false;

    /** cut border */
    is_first_enter_cut_border_last_qr_detection_time_ = true;
    last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
    is_first_cut_border_mode_end_ = true;

    // Slip detection
    last_zero_velocity_time_ = std::chrono::steady_clock::now(); // Record start time when velocity is close to 0
    is_velocity_zero_ = false;                                   // Mark if current velocity is close to 0

    is_recovery_active_.store(false);

    // Wait for async task to finish (if started)
    if (unstake_future_.valid())
    {
        unstake_future_.wait(); // Or consider wait_for with timeout
    }

    // Unstake
    is_unstaking_ = false;

    last_imu_timestamp_ = 0;
    motion_detection_timestamp_ = 0;

    is_slipping_detected_.store(false);
    is_stuck_detected_.store(false);

#if STUCK
    SetStuckDetectionActive(false);
    SetResetAllStuckStates();
#endif

    last_perception_movement_state_ = PerceptionMovementState::IDLE;          // 上次运动状态
    last_perception_movement_time_ = std::chrono::steady_clock::time_point{}; // 上次运动状态改变时间

    // Reset IMU processor state
    if (imu_processor_)
    {
        imu_processor_->ResetState();
    }

    last_bottom_obstacle_avoidance_time_ = std::chrono::steady_clock::time_point{};
}

void NavigationMowerAlg::SetQRCodeLocationResult(const QRCodeLocationResult &qrcode_loc_result)
{
    if (save_qr_data_)
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            qr_detect_x_.push_back(qrcode_loc_result.xyzrpw.x);
            qr_detect_y_.push_back(qrcode_loc_result.xyzrpw.y);
            qr_detect_yaw_.push_back(qrcode_loc_result.xyzrpw.w);
        }
    }
}

/**
 * @brief
 *
 * @param qr_x_set qr_y_set qr_yaw_set
 * @return qr_x_avg qr_y_avg qr_yaw_avg
 */
std::vector<float> NavigationMowerAlg::Process_QRdata(std::vector<float> qr_x_set, std::vector<float> qr_y_set, std::vector<float> qr_yaw_set)
{
    std::sort(qr_x_set.begin(), qr_x_set.end());
    std::sort(qr_y_set.begin(), qr_y_set.end());
    std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    while ((qr_x_set[qr_x_set.size() - 1] - qr_x_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_x_set.size(); i++)
        {
            std::cout << qr_x_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_x_set.size() <= 2)
        {
            break;
        }
        qr_x_set.erase(qr_x_set.begin());
        qr_x_set.pop_back();
        std::sort(qr_x_set.begin(), qr_x_set.end());
    }
    while ((qr_y_set[qr_y_set.size() - 1] - qr_y_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_y_set.size(); i++)
        {
            std::cout << qr_y_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_y_set.size() <= 2)
        {
            break;
        }
        qr_y_set.erase(qr_y_set.begin());
        qr_y_set.pop_back();
        std::sort(qr_y_set.begin(), qr_y_set.end());
    }
    while ((qr_yaw_set[qr_yaw_set.size() - 1] - qr_yaw_set[0]) > 0.3)
    {
        for (size_t i = 0; i < qr_yaw_set.size(); i++)
        {
            std::cout << qr_yaw_set[i] << ":";
        }
        std::cout << std::endl;
        if (qr_yaw_set.size() <= 2)
        {
            break;
        }
        qr_yaw_set.erase(qr_yaw_set.begin());
        qr_yaw_set.pop_back();
        std::sort(qr_yaw_set.begin(), qr_yaw_set.end());
    }
    float qr_x_avg = std::accumulate(qr_x_set.begin(), qr_x_set.end(), 0.0) / qr_x_set.size();
    float qr_y_avg = std::accumulate(qr_y_set.begin(), qr_y_set.end(), 0.0) / qr_y_set.size();
    float qr_yaw_avg = std::accumulate(qr_yaw_set.begin(), qr_yaw_set.end(), 0.0) / qr_yaw_set.size();
    LOG_DEBUG("QRCodeData : qr_x_avg:{}, qr_y_avg:{}, qr_yaw_avg:{}.", qr_x_avg, qr_y_avg, qr_yaw_avg);
    std::vector<float> qr_data;
    qr_data.push_back(qr_x_avg);
    qr_data.push_back(qr_y_avg);
    qr_data.push_back(qr_yaw_avg);
    return qr_data;
}

/**
 * @brief Collect QR code data
 *
 * @return QR data
 */
std::vector<float> NavigationMowerAlg::Collect_QRdata()
{
    PublishVelocity(0, 0, stay_time_);
    save_qr_data_ = true;
    qr_detect_x_.clear();
    qr_detect_y_.clear();
    qr_detect_yaw_.clear();
    uint64_t record_times_ = 0;
    while (!(qr_detect_x_.size() >= save_data_num_))
    {
        PublishVelocity(0, 0, stay_time_);
        record_times_++;
        LOG_DEBUG("Stay And Collect Data, record_time :{}", record_times_ * stay_time_);
        if (record_times_ >= (stay_all_time_ / stay_time_))
        {
            LOG_DEBUG("Qrcode Collect Timeout!");
            std::vector<float> qr_error_data{0, 0, 0};
            return qr_error_data;
        }
    }
    save_qr_data_ = false;
    std::vector<float> qr_avg_data = Process_QRdata(qr_detect_x_, qr_detect_y_, qr_detect_yaw_);
    return qr_avg_data;
}

void NavigationMowerAlg::ProcessQRcodeAndUnstake(const std::vector<float> &qrcode_result)
{
    if (fabs(qrcode_result[0]) == 0)
    {
        LOG_DEBUG("QRCodeData Failed And Turn 45 Angle");
        // ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
        ControlRotaryMotionWithIMU(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);
    }
    else
    {
        if (fabs(qrcode_result[1]) > (charge_station_width_ / 2))
        {
            if (qrcode_result[1] > 0)
            {
                LOG_DEBUG("Y > 0 And More Than Charge Width And Turn Angle");
                // ControlRotaryMotion(M_PI * angle_proportion_ + fabs(qrcode_result[2]), 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
                ControlRotaryMotionWithIMU(M_PI * angle_proportion_ + fabs(qrcode_result[2]), 0.0, unstake_vel_angular_);
            }
            else
            {
                LOG_DEBUG("Y < 0 And More Than Charge Width And Turn Angle");
                float angle_0 = std::atan2(fabs(qrcode_result[1]) + (charge_station_width_ / 2),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                // ControlRotaryMotion(-fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0,
                // 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
                ControlRotaryMotionWithIMU(-fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0,
                                           0.0, unstake_vel_angular_);
            }
        }
        else
        {
            if (qrcode_result[1] > 0)
            {
                LOG_DEBUG("Y > 0 And Less Than Charge Width And Turn Angle");
                float angle_0 = std::atan2((charge_station_width_ / 2) - fabs(qrcode_result[1]),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                // ControlRotaryMotion(fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0,
                // 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
                ControlRotaryMotionWithIMU(fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0,
                                           0.0, unstake_vel_angular_);
            }
            else
            {
                LOG_DEBUG("Y < 0 And Less Than Charge Width And Turn Angle");
                float angle_0 = std::atan2(fabs(qrcode_result[1]) + (charge_station_width_ / 2),
                                           fabs(qrcode_result[0]) - charge_station_longth_);
                // ControlRotaryMotion(-fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0, 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
                ControlRotaryMotionWithIMU(-fabs(qrcode_result[2]) + angle_proportion_ * M_PI + (1 - angle_proportion_) * angle_0, 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station
            }
        }
    }
}

void NavigationMowerAlg::PerformUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    (void)qrcode_loc_result;
    LOG_INFO("[PerformUnstakeMode1] Start executing unstake operation");

    // ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // Exit charging station
    ControlLinearMotionWithIMUThread(unstake_distance_, 0.0, unstake_vel_linear_, -1);

    // ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);  // Rotate 45 degrees away from charging station
    std::vector<float> stable_data = Collect_QRdata();
    LOG_DEBUG("Stable QRCodeData : x:{}, y:{}, yaw:{}.", stable_data[0], stable_data[1], stable_data[2]);
    ProcessQRcodeAndUnstake(stable_data);
    PublishVelocity(0.0, 0.0, 1000); // Stop for 1s

    if (is_on_grass_field_) // On grass
    {
        is_unstake_mode_completed_ = true; // Unstake completed
        is_unstake_success_ = true;        // Unstake successful

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report success
        LOG_INFO("[PerformUnstakeMode1] On grass. Unstake mode succeeded, start mowing mode");
    }
    else // Not on grass
    {
        is_unstake_mode_completed_ = true; // Unstake completed
        is_unstake_success_ = false;       // Unstake failed

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report failure
        LOG_ERROR("[PerformUnstakeMode1] Not on grass. Unstake mode failed, report error");

        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
    }
}

void NavigationMowerAlg::PerformUnstakeModeAsync(const QRCodeLocationResult &qrcode_loc_result)
{
    // If an unstake task is already running, do not start a new one
    if (unstake_future_.valid() && unstake_future_.wait_for(std::chrono::seconds(0)) != std::future_status::ready)
    {
        LOG_WARN("[PerformUnstakeModeAsync1] Unstake task already running, skip new task");
        return;
    }

    // Start async task to execute unstake operation
    unstake_future_ = std::async(std::launch::async, [this, qrcode_loc_result]() {
        std::unique_lock<std::mutex> lock(recovery_mutex_);

        // while (is_recovery_active_)
        // {
        //     LOG_INFO("[PerformUnstakeModeAsync1] Recovery operation detected, suspend unstake task");
        //     recovery_cv_.wait(lock); // Wait for recovery operation to complete
        // }

        LOG_ERROR("[PerformUnstakeModeAsync1] Start executing unstake operation");
        // ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // Exit charging station
        ControlLinearMotionWithIMUThread(unstake_distance_, 0.0, unstake_vel_linear_, -1);

        std::vector<float> stable_data = Collect_QRdata();
        LOG_DEBUG("Stable QRCodeData : x:{}, y:{}, yaw:{}.", stable_data[0], stable_data[1], stable_data[2]);
        ProcessQRcodeAndUnstake(stable_data);
        PublishVelocity(0.0, 0.0, 1000); // Stop for 1s

        if (is_on_grass_field_) // On grass
        {
            is_unstake_mode_completed_ = true; // Unstake completed
            is_unstake_success_ = true;        // Unstake successful

            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report success
            LOG_ERROR("[PerformUnstakeModeAsync1] On grass. Unstake mode succeeded, start mowing mode");
        }
        else // Not on grass
        {
            is_unstake_mode_completed_ = true; // Unstake completed
            is_unstake_success_ = false;       // Unstake failed

            SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report failure
            LOG_ERROR("[PerformUnstakeModeAsync1] Not on grass. Unstake mode failed, report error");

            PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
        }
    });
}
void NavigationMowerAlg::HandleRecoveryStart()
{
    std::lock_guard<std::mutex> lock(recovery_mutex_);
    is_recovery_active_ = true;
    LOG_INFO("[HandleRecoveryStart1] Recovery operation started, suspending unstake task");
}

void NavigationMowerAlg::HandleRecoveryEnd()
{
    {
        std::lock_guard<std::mutex> lock(recovery_mutex_);
        is_recovery_active_ = false;
    }
    recovery_cv_.notify_all(); // Notify suspended unstake tasks to continue
    LOG_INFO("[HandleRecoveryEnd1] Recovery operation ended, resuming unstake task");
}

void NavigationMowerAlg::SetSlippingStatus(bool is_slipping)
{
    if (vel_publisher_)
    {
        vel_publisher_->SetSlippingStatus(is_slipping); // Set slipping status
    }
}

void NavigationMowerAlg::PerformUnstakeMode()
{
    // ControlLinearMotion(unstake_distance_, 0.0, unstake_vel_linear_, -1); // Exit charging station
    ControlLinearMotionWithIMUThread(unstake_distance_, 0.0, unstake_vel_linear_, -1);

    // ControlRotaryMotion(unstake_adjust_yaw_, 0.0, unstake_vel_angular_);  // Rotate 45 degrees away from charging station
    ControlRotaryMotionWithIMU(unstake_adjust_yaw_, 0.0, unstake_vel_angular_); // Rotate 45 degrees away from charging station

    PublishVelocity(0.0, 0.0, 1000); // Stop for 1s

    if (is_on_grass_field_) // On grass
    {
        is_unstake_mode_completed_ = true; // Unstake completed
        is_unstake_success_ = true;        // Unstake successful

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report success
        LOG_INFO("[PerformUnstakeMode1] On grass. Unstake mode succeeded, start mowing mode");
    }
    else // Not on grass
    {
        is_unstake_mode_completed_ = true; // Unstake completed
        is_unstake_success_ = false;       // Unstake failed

        SetUndockResult(is_unstake_mode_completed_, is_unstake_success_); // Report failure
        LOG_ERROR("[PerformUnstakeMode1] Not on grass. Unstake mode failed, reporting error");

        PublishException(SocExceptionLevel::ERROR, SocExceptionValue::ALG_PNC_NOT_GRASS_EXCEPTION);
    }
}

/**
 * @brief Determine if on grass field
 *
 * @param segment_detect_result
 * @return true
 * @return false
 */
bool NavigationMowerAlg::IsGrassField(const GrassDetectStatus &grass_detect_status)
{
    // Add new status to queue
    frames_.push_back(grass_detect_status);

    // If queue is full (more than 10 frames), remove the oldest frame
    if (frames_.size() >= 11)
    {
        frames_.pop_front();
    }

    /**
     * NO_GRASS = 0,                // No grass (all obstacles)
     * HAVE_GRASS_NO_OBSTACLE = 1,  // Grass, no obstacles (all grass)
     * HAVE_GRASS_HAVE_OBSTACLE = 2 // Grass and obstacles (part grass, part obstacles)
     */

    if (frames_.size() >= 10)
    {
        size_t grass_count = 0; // Grass count
        for (const auto &status : frames_)
        {
            if (status == GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE || status == GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE)
            {
                grass_count++; // Grass
            }
        }
        LOG_DEBUG("[BeaconDetection1] Grass count grass_count({})", grass_count);

        int grass_count_threshold = 7;
        if (int(grass_count) > grass_count_threshold) // Determine if it's grass
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    // Less than 10 frames, cannot determine
    return false;
}

void NavigationMowerAlg::PerformRandomMowing()
{
    thread_control_ = ThreadControl::RANDOM_MOWING_THREAD;
    UpdateFeatureSelection(thread_control_);
}

void NavigationMowerAlg::PerformSpiralMowing()
{
    thread_control_ = ThreadControl::SPIRAL_MOWING_THREAD;
    UpdateFeatureSelection(thread_control_);
}

void NavigationMowerAlg::HandleExploreCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection1] Exit cross-region from non-grass to grass");

        if (edge_mode_direction_ == 1) // Edge following clockwise
        {
            LOG_INFO("[CrossRegion] Clockwise edge following cross-region not considered");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_);
        }
        else // Edge following counterclockwise
        {
            LOG_INFO("[BeaconDetection1] Counterclockwise edge following, turning right by a certain angle");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_);
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection1] Close cross-region, switch to recharge");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1;
        }
        else
        {
            LOG_INFO("[BeaconDetection1] Close cross-region, switch to edge following");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now();

            if (area_calc_start_callback_)
            {
                area_calc_start_callback_(GetSteadyClockTimestampMs());
                // PublishVelocity(0.0, 0.0, 5000);
            }
        }

        mcu_triggers_cross_region_ = false;
        UpdateCrossRegionRunningState(CrossRegionRunningState::FINISH);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection1] Exit cross-region by beacon detection");

        if (edge_mode_direction_ == 1)
        {
            LOG_INFO("[CrossRegion] Clockwise edge following cross-region not considered");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_);
        }
        else
        {
            LOG_INFO("[BeaconDetection1] Counterclockwise edge following, turning right by a certain angle!");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_);
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection1] Close cross-region, switch to recharge");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1;
        }
        else
        {
            LOG_INFO("[BeaconDetection1] Close cross-region, switch to edge following");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now();

            if (area_calc_start_callback_)
            {
                area_calc_start_callback_(GetSteadyClockTimestampMs());
                // PublishVelocity(0.0, 0.0, 5000);
            }
        }

        mcu_triggers_cross_region_ = false;
        UpdateCrossRegionRunningState(CrossRegionRunningState::FINISH);
    }
}

void NavigationMowerAlg::HandleNormalCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection1] Exit cross-region from non-grass to grass");

        // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
        // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_);

        mcu_triggers_cross_region_ = false;
        UpdateCrossRegionRunningState(CrossRegionRunningState::FINISH);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection1] Exit cross-region by beacon detection");

        // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
        // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_);

        mcu_triggers_cross_region_ = false;
        UpdateCrossRegionRunningState(CrossRegionRunningState::FINISH);
    }
}

bool NavigationMowerAlg::SetMarkLocationMarkId(int mark_id)
{
    if (set_mark_id_callback_)
    {
        return set_mark_id_callback_(mark_id);
    }
    return false;
}

void NavigationMowerAlg::UpdateCrossRegionRunningState(CrossRegionRunningState state)
{
    if (cross_region_running_state_callback_)
    {
        cross_region_running_state_callback_(state);
    }
}

void NavigationMowerAlg::SetUndockResult(bool completed, bool result, mower_msgs::srv::UndockOperationStatus status)
{
    if (undock_result_callback_)
    {
        undock_result_callback_(completed, result, status);
    }
}

void NavigationMowerAlg::FingVaidBeaconIdx(const std::vector<MarkIdDistance> &mark_id_distance_vec, int &shortest_dis_inx)
{
    float min_distance = std::numeric_limits<float>::max();
    shortest_dis_inx = -1;

    for (size_t i = 0; i < mark_id_distance_vec.size(); ++i)
    {
        if (mark_id_distance_vec[i].distance < mark_distance_threshold_) // 50cm
        {
            if (mark_id_distance_vec[i].distance < min_distance)
            {
                min_distance = mark_id_distance_vec[i].distance;
                shortest_dis_inx = i;
            }
        }
    }
}

void NavigationMowerAlg::HandlePerceptionBasedForwardBackwardMovement(const PerceptionFusionResult &fusion_result)
{
    LOG_INFO("[Mower] Adjust direction forward or backward based on perception!");

    // 获取占用栅格数据
    const auto &occupancy_grid = fusion_result.occupancy_grid;
    if (occupancy_grid.grid.empty() || occupancy_grid.width <= 0 || occupancy_grid.height <= 0)
    {
        LOG_WARN("[Mower] Invalid occupancy grid data in PerceptionBasedAdjustment!");
        PublishVelocity(0, 0);
        return;
    }

    bool has_bottom_obstacle_original = ObstacleClassification::HasBottomObstacle(occupancy_grid.grid, occupancy_grid.height, occupancy_grid.width, 0.01f);
    bool has_bottom_beacon_obstacle = ObstacleClassification::HasBottomBeaconObstacle(occupancy_grid.grid, occupancy_grid.height, occupancy_grid.width);
    bool has_bottom_obstacle = has_bottom_obstacle_original && has_bottom_beacon_obstacle;
    auto current_time = std::chrono::steady_clock::now();

    // 确定期望的运动状态
    PerceptionMovementState desired_state = has_bottom_obstacle ? PerceptionMovementState::BACKWARD : PerceptionMovementState::FORWARD;

    // 防抖动逻辑：只有在状态改变且经过足够时间间隔后才执行新的运动命令
    if (desired_state != last_perception_movement_state_)
    {
        // 检查是否经过了足够的防抖动时间
        if (last_perception_movement_time_.time_since_epoch().count() == 0 ||
            std::chrono::duration<float>(current_time - last_perception_movement_time_).count() >= perception_movement_debounce_time_)
        {
            // 执行新的运动命令
            if (desired_state == PerceptionMovementState::BACKWARD)
            {
                LOG_INFO("[Mower] Bottom obstacle detected, moving backward!");
                PublishVelocity(-mower_linear_, 0);
            }
            else
            {
                LOG_INFO("[Mower] No bottom obstacle, moving forward!");
                PublishVelocity(mower_linear_, 0);
            }

            // 更新状态和时间
            last_perception_movement_state_ = desired_state;
            last_perception_movement_time_ = current_time;
        }
        else
        {
            // 防抖动期间，保持当前运动状态
            LOG_INFO("[Mower] Anti-jitter: maintaining current movement state for {:.2f}s more",
                     perception_movement_debounce_time_ - std::chrono::duration<float>(current_time - last_perception_movement_time_).count());

            // 继续执行上次的运动命令
            if (last_perception_movement_state_ == PerceptionMovementState::BACKWARD)
            {
                LOG_INFO("[Mower] Continuing backward movement");
                PublishVelocity(-mower_linear_, 0);
            }
            else if (last_perception_movement_state_ == PerceptionMovementState::FORWARD)
            {
                LOG_INFO("[Mower] Continuing forward movement");
                PublishVelocity(mower_linear_, 0);
            }
            else
            {
                LOG_INFO("[Mower] Invalid perception movement state, stopping movement");
                PublishVelocity(0, 0);
            }
        }
    }
    else
    {
        // 状态未改变，继续执行当前运动命令
        if (last_perception_movement_state_ == PerceptionMovementState::BACKWARD)
        {
            LOG_INFO("[Mower] Continuing backward movement (same state)");
            PublishVelocity(-mower_linear_, 0);
        }
        else if (last_perception_movement_state_ == PerceptionMovementState::FORWARD)
        {
            LOG_INFO("[Mower] Continuing forward movement (same state)");
            PublishVelocity(mower_linear_, 0);
        }
        else
        {
            LOG_INFO("[Mower] Idle state, stopping movement");
            PublishVelocity(0, 0);
        }
    }
}

void NavigationMowerAlg::PerceptionBasedAdjustment(const MarkLocationResult &mark_loc_result, const PerceptionFusionResult &fusion_result)
{
    switch (mark_loc_result.mark_perception_direction)
    {
    case -1: // Leftward deviation, turn left
    {
        LOG_INFO("[Mower] Adjust direction leftward based on perception!");
        PublishVelocity(0, mower_angular_);
        // 重置运动状态为空闲
        last_perception_movement_state_ = PerceptionMovementState::IDLE;
        break;
    }

    case 0: // Centered, go forward or backward with anti-jitter mechanism
    {
        HandlePerceptionBasedForwardBackwardMovement(fusion_result);
        break;
    }

    case 1: // Rightward deviation, turn right
    {
        LOG_INFO("[Mower] Adjust direction rightward based on perception!");
        PublishVelocity(0, -mower_angular_);
        // 重置运动状态为空闲
        last_perception_movement_state_ = PerceptionMovementState::IDLE;
        break;
    }

    default:
    {
        LOG_INFO("[Mower] Error in the mark_perception_direction flag based on perception!");
        PublishVelocity(0, 0);
        // 重置运动状态为空闲
        last_perception_movement_state_ = PerceptionMovementState::IDLE;
        break;
    }
    }
}

void NavigationMowerAlg::UpdateFeatureSelection(const ThreadControl &thread_control, std::vector<BehaviorExceptionType> behavior_exception_types)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData rechgarge{ThreadControl::RECHARGE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData edge_follow{ThreadControl::PERCEPTION_EDGE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData cross_region{ThreadControl::CROSS_REGION_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData random_mower{ThreadControl::RANDOM_MOWING_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData behavior{ThreadControl::BEHAVIOR_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData spiral_mower{ThreadControl::SPIRAL_MOWING_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    FeatureSelectData escape{ThreadControl::ESCAPE_THREAD, static_cast<int>(NavAlgCtrlState::IGNORE)};
    bool is_recover_from_exception = false;
    bool is_behavior_loop = false;
    std::vector<BehaviorExceptionType> last_triggered_exception_types;
    if (is_recover_from_exception_ && thread_control != ThreadControl::UNDEFINED)
    {
        LOG_INFO("recover from exception thread_control: {} is_behavior_loop_: {} last_triggered_exception_types size: {}",
                 static_cast<int>(thread_control), is_behavior_loop_, last_triggered_exception_types_.size());
        is_recover_from_exception = true;
        is_recover_from_exception_ = false;
        is_behavior_loop = is_behavior_loop_;
        is_behavior_loop_ = false;
        last_triggered_exception_types = last_triggered_exception_types_;
        last_triggered_exception_types_.clear();
    }

    switch (thread_control)
    {
    case ThreadControl::PERCEPTION_EDGE_THREAD:                               // Edge following: includes escape
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);   // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        // rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);        // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Edge following: includes escape");
        break;

    case ThreadControl::CROSS_REGION_THREAD:                                  // Cross region: includes edge following, escape
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);   // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE); // 3. Cross region
        // rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Cross region: includes edge following, escape");
        break;

    case ThreadControl::RECHARGE_THREAD:                                      // Recharge: includes edge following, cross region, escape
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        // cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);     // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Recharge: includes edge following, cross region, escape");
        break;

    case ThreadControl::RANDOM_MOWING_THREAD:
    {
        if (random_mower_state_ == RandomMowerRunningState::NORMAL)
        {
            random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. Random mowing
            edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
            cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
            rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
            behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
            spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
            escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        }
        else if (random_mower_state_ == RandomMowerRunningState::TRAP_WAIT_BIAS)
        {
            random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. Random mowing
            edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
            cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
            rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
            behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
            spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
            escape.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);        // 7. Escape
        }
        else if (random_mower_state_ == RandomMowerRunningState::TRAP_EDGE_FOLLOW)
        {
            random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE); // 1. Random mowing
            // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);   // 2. Edge following
            cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
            rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
            behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
            spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
            escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        }
        else
        {
            random_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 1. Random mowing
            edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
            cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
            rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
            behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
            spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
            escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        }
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Random mowing:");
        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:                                      // Recovery:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        // rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE); // 5. Recovery
        behavior.behavior_exception_types = behavior_exception_types;
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Recovery:");
        break;

    case ThreadControl::SPIRAL_MOWING_THREAD:                                 // Spiral mowing:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::ENABLE);  // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Spiral mowing:");
        break;

    case ThreadControl::CLOSE_ALL_TASK:                                       // Close all tasks:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Close all tasks:");
        break;

    case ThreadControl::UNDEFINED:                                            // Undefined:
        random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        // rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        LOG_INFO_THROTTLE(2000, "[FeatureSelection1] FeatureSelection: Undefined:");
        break;

    default:
        // random_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 1. Random mowing
        // edge_follow.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);  // 2. Edge following
        // cross_region.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 3. Cross region
        // rechgarge.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);    // 4. Recharge
        // behavior.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);     // 5. Recovery
        // spiral_mower.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE); // 6. Spiral mowing
        // escape.alg_status = static_cast<int>(NavAlgCtrlState::DISABLE);       // 7. Escape
        break;
    }

    feature_data.push_back(random_mower);
    feature_data.push_back(edge_follow);
    feature_data.push_back(cross_region);
    feature_data.push_back(rechgarge);
    feature_data.push_back(behavior);
    feature_data.push_back(spiral_mower);
    feature_data.push_back(escape);

    for (auto &feature : feature_data)
    {
        feature.is_recover_from_exception = is_recover_from_exception;
        feature.is_behavior_loop = is_behavior_loop;
        feature.last_triggered_exception_types = last_triggered_exception_types;
    }

    if (feature_select_callback_)
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationMowerAlg::SetMowerAlgParam(const MowerAlgParam &param)
{
    is_enable_unstake_mode_ = param.is_enable_unstake_mode;
    unstake_distance_ = param.unstake_distance;       // Unstake distance /*param*/
    unstake_adjust_yaw_ = param.unstake_adjust_yaw;   // Unstake adjustment angle /*param*/
    unstake_vel_linear_ = param.unstake_vel_linear;   // Unstake linear velocity /*param*/
    unstake_vel_angular_ = param.unstake_vel_angular; // Unstake angular velocity /*param*/

    // Algorithm parameters
    mower_linear_ = param.mower_linear;   /*param*/
    mower_angular_ = param.mower_angular; /*param*/
    // perception_drive_cooldown_time_ = param.perception_drive_cooldown_time; // Perception drive cooldown time 5s /*param*/
    edge_mode_direction_ = param.edge_mode_direction;                   // Default counterclockwise -1 /*param*/
    cross_region_adjust_yaw_ = param.cross_region_adjust_yaw;           // Yaw adjustment after cross-region /*param*/
    cross_region_adjust_displace_ = param.cross_region_adjust_displace; // Displacement adjustment after cross-region /*param*/
    mark_distance_threshold_ = param.mark_distance_threshold;           // 1.5 Beacon distance threshold relative to robot camera, used to determine if within region /*param*/
    camera_2_center_dis_ = param.camera_2_center_dis;                   // Distance from robot camera to rotation center is 0.45 /*param*/

    edge_perception_drive_cooldown_time_threshold_ = param.edge_perception_drive_cooldown_time_threshold; // 10s Edge perception drive cooldown time  /*param*/
    qr_detection_cooldown_time_threshold_ = param.qr_detection_cooldown_time_threshold;                   // 60s QR code detection cooldown time  /*param*/
    mark_detection_cooldown_time_threshold_ = param.mark_detection_cooldown_time_threshold;               // 60s Beacon detection cooldown time  /*param*/

    // Stuck detection data logging control
    enable_stuck_detection_data_logging_ = param.enable_stuck_detection_data_logging; // Enable stuck detection data logging /*param*/
}

void NavigationMowerAlg::SetMowerRunningState(MowerRunningState state)
{
    LOG_INFO_THROTTLE(1000, "NavigationMowerAlg running state: {}", static_cast<int>(state));
    mower_running_state_ = state;
    if (state == MowerRunningState::RUNNING)
    {
        ResumeVelocity();
    }
    else if (state == MowerRunningState::PAUSE)
    {
        PauseVelocity();
    }
    else
    {
        LOG_WARN("[NavigationMowerAlg] Unknown state {}!", static_cast<int>(state));
    }
}

void NavigationMowerAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationMowerAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationMowerAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationMowerAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationMowerAlg::DataConversion(MarkLocationResult &mark_loc_result)
{
    LOG_DEBUG_THROTTLE(1000, "[BeaconDetection1] Before coordinate conversion MarkLocation result: detect_status: {} mark_perception_status: {} mark_perception_direction: {} roi_confidence: {} "
                             "target_direction : {} markID : {} v_markID_dis.size : {} xyz({} {} {}) yaw({})",
                       mark_loc_result.detect_status, mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                       mark_loc_result.roi_confidence, mark_loc_result.target_direction, mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                       mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                       Radians2Degrees(mark_loc_result.xyzrpw.w));

    if (mark_loc_result.roi_confidence >= 60 && mark_loc_result.roi_confidence <= 100) // 60~100
    {
        mark_loc_result.roi_confidence = 1; // 1 means in ROI area
    }
    else if (mark_loc_result.roi_confidence < 60 && mark_loc_result.roi_confidence >= 0) // 0~60
    {
        mark_loc_result.roi_confidence = 0; // 0 means not in ROI area
    }
    else
    {
        mark_loc_result.roi_confidence = -1; // -1 means detection failed
    }

    // 2. Convert camera-to-mark (right-hand) coordinates to base_link-to-mark coordinates
    if (mark_loc_result.detect_status == 2)
    {
        // Camera to base_link fixed coordinates
        Pose_Mark camera_to_base_link = {camera_2_center_dis_, 0.0, 0.0, 0.0, 0.0, 0.0};

        // Camera to mark coordinates
        Pose_Mark camera_to_mark = {mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                                    mark_loc_result.xyzrpw.r, mark_loc_result.xyzrpw.p, mark_loc_result.xyzrpw.w};

        // Calculate base_link relative to mark
        Pose_Mark base_link_to_mark = calculateBaseLinkRelativeToMark(camera_to_mark, camera_to_base_link);

        mark_loc_result.xyzrpw.x = base_link_to_mark.x;
        mark_loc_result.xyzrpw.y = base_link_to_mark.y;
        mark_loc_result.xyzrpw.z = base_link_to_mark.z;
        mark_loc_result.xyzrpw.r = base_link_to_mark.roll;
        mark_loc_result.xyzrpw.p = base_link_to_mark.pitch;
        mark_loc_result.xyzrpw.w = base_link_to_mark.yaw;

        LOG_DEBUG("[BeaconDetection1] After coordinate conversion MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
                  "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
                  mark_loc_result.detect_status,
                  mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
                  mark_loc_result.roi_confidence, mark_loc_result.target_direction,
                  mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
                  mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
                  Radians2Degrees(mark_loc_result.xyzrpw.w));
    }
}

void NavigationMowerAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationMowerAlg::SetMarkLocationMarkIdCallback(std::function<bool(int)> callback)
{
    set_mark_id_callback_ = callback;
}

void NavigationMowerAlg::SetCrossRegionRunningStateCallback(std::function<void(CrossRegionRunningState)> callback)
{
    cross_region_running_state_callback_ = callback;
}

void NavigationMowerAlg::SetUndockResultCallback(std::function<void(bool, bool, mower_msgs::srv::UndockOperationStatus)> callback)
{
    undock_result_callback_ = callback;
}

void NavigationMowerAlg::SetAreaCalcStartCallback(std::function<bool(uint64_t)> callback)
{
    area_calc_start_callback_ = callback;
}

void NavigationMowerAlg::SetAreaCalcStopCallback(std::function<bool(uint64_t, float &, float &)> callback)
{
    area_calc_stop_callback_ = callback;
}

void NavigationMowerAlg::SetRegionExploreResultCallback(std::function<void(RegionExploreResult &)> callback)
{
    region_explore_result_callback_ = callback;
}

void NavigationMowerAlg::SetCutBorderResultCallback(std::function<void(bool, bool)> callback)
{
    cut_border_result_callback_ = callback;
}

void NavigationMowerAlg::SetPerceptionLocalizationAlgCtrlCallback(std::function<void(const ob_mower_msgs::PerceptionLocalizationAlgCtrl &)> callback)
{
    perception_localization_alg_ctrl_callback_ = callback;
}

void NavigationMowerAlg::SetEdgeFollowStatusCallback(std::function<void(int)> callback)
{
    edge_follow_status_callback_ = callback;
}

void NavigationMowerAlg::SetAppTriggersRecharge(bool is_recharge)
{
    mcu_triggers_recharge_ = is_recharge;
    LOG_INFO_THROTTLE(1000, "[BeaconDetection1] MCU recharge request: mcu_triggers_recharge_({})", int(mcu_triggers_recharge_));
}

void NavigationMowerAlg::SetAppTriggersCrossRegion(bool is_cross_region)
{
    mcu_triggers_cross_region_ = is_cross_region; // MCU triggers cross-region thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection1] MCU cross-region request: mcu_triggers_cross_region_({})", int(mcu_triggers_cross_region_));
}

void NavigationMowerAlg::SetAppTriggersMower(bool is_mower)
{
    mcu_triggers_mower_ = is_mower; // MCU triggers random mowing thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection1] MCU mowing request: mcu_triggers_mower_({})", int(mcu_triggers_mower_));
}

void NavigationMowerAlg::SetAppTriggersSpiralMower(bool is_mower)
{
    mcu_triggers_spiral_mower_ = is_mower; // MCU triggers spiral mowing thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection1] MCU spiral mowing request: mcu_triggers_spiral_mower_({})", int(mcu_triggers_spiral_mower_));
}

void NavigationMowerAlg::SetAppTriggersRegionExplore(bool is_region_explore)
{
    mcu_triggers_region_exploration_ = is_region_explore; // MCU triggers region exploration thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection1] MCU region exploration request: mcu_triggers_region_exploration_({})", int(mcu_triggers_region_exploration_));
}

void NavigationMowerAlg::SetAppTriggersCutBorder(bool is_cut_border)
{
    mcu_triggers_cut_border_ = is_cut_border; // MCU triggers cut border thread
    LOG_INFO_THROTTLE(1000, "[BeaconDetection1] MCU cut border request: mcu_triggers_cut_border_({})", int(mcu_triggers_cut_border_));
}

void NavigationMowerAlg::ShowMowerRunningInfo(const MarkLocationResult &mark_loc_result)
{
    (void)mark_loc_result;
    LOG_INFO_THROTTLE(2000, "[BeaconDetection1] MCU recharge request: mcu_triggers_recharge_({})", int(mcu_triggers_recharge_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection1] MCU cross-region request: mcu_triggers_cross_region_({})", int(mcu_triggers_cross_region_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection1] MCU (random) mowing request: mcu_triggers_mower_({})", int(mcu_triggers_mower_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection1] MCU spiral mowing request: mcu_triggers_spiral_mower_({})", int(mcu_triggers_spiral_mower_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection1] MCU exploration request: mcu_triggers_region_exploration_({})", int(mcu_triggers_region_exploration_));
    LOG_INFO_THROTTLE(2000, "[BeaconDetection1] MCU cut border request: mcu_triggers_cut_border_({})", int(mcu_triggers_cut_border_));

#if (TEST == 0)
    /********************************************************Undocking print****************************************************************** */
    if (is_enable_unstake_mode_ && !is_unstake_success_ &&                                                                                                /* Unstake mode enabled && not successful */
        !is_power_connected_ &&                                                                                                                           /* Robot not powered */
        (mcu_triggers_cut_border_ || mcu_triggers_region_exploration_ || mcu_triggers_mower_)) /* MCU cut border request */ /* MCU exploration request */ /* MCU mowing request */
    {
        if (is_unstake_mode_completed_) // Unstake completed
        {
            LOG_ERROR_THROTTLE(1000, "[BeaconDetection1] Unstake failed, reporting error state!");
        }
        else // Unstake not completed
        {
            // LOG_WARN_THROTTLE(1000, "[BeaconDetection1] Mower not powered, cannot unstake");
        }
    }

    /********************************************************Undocking print****************************************************************** */
#endif

    /********************************************************thread_control_****************************************************************** */
    // switch (thread_control_)
    // {
    // case ThreadControl::PERCEPTION_EDGE_THREAD:
    //     LOG_DEBUG("[BeaconDetection1] Running mode: Edge following({})", int(thread_control_));
    //     break;

    // case ThreadControl::CROSS_REGION_THREAD:
    //     LOG_DEBUG("[BeaconDetection1] Running mode: Cross-region({})", int(thread_control_));
    //     break;

    // case ThreadControl::RECHARGE_THREAD:
    //     LOG_DEBUG("[BeaconDetection1] Running mode: Recharge({})", int(thread_control_));
    //     break;

    // case ThreadControl::RANDOM_MOWING_THREAD:
    //     LOG_DEBUG("[BeaconDetection1] Running mode: Random mowing({})", int(thread_control_));
    //     break;

    // default:
    //     LOG_DEBUG("[BeaconDetection1] Not running any mode");
    //     break;
    // }

    /********************************************************mark_loc_result****************************************************************** */

    // LOG_DEBUG("[BeaconDetection1]  MarkLocation result: detect_status({}) mark_perception_status({}) mark_perception_direction({}) "
    //           "roi_confidence({}) target_direction({}) markID({}) v_markID_dis.size({}) xyz({} {} {}) yaw({})",
    //           mark_loc_result.detect_status,
    //           mark_loc_result.mark_perception_status, mark_loc_result.mark_perception_direction,
    //           mark_loc_result.roi_confidence, mark_loc_result.target_direction,
    //           mark_loc_result.mark_id, mark_loc_result.mark_id_distance.size(),
    //           mark_loc_result.xyzrpw.x, mark_loc_result.xyzrpw.y, mark_loc_result.xyzrpw.z,
    //           Radians2Degrees(mark_loc_result.xyzrpw.w));

    // for (const auto &mark_id_distance : mark_loc_result.mark_id_distance)
    // {
    //     LOG_DEBUG("[BeaconDetection1] mark_id_distance beacon and distance: mark_id({}), distance({})",
    //               mark_id_distance.mark_id, mark_id_distance.distance);
    // }
}

void NavigationMowerAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};

    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);

    feature_data.push_back(feature);

    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationMowerAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationMowerAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationMowerAlg::CrossRegionDisable()
{
    DealFeatureSelect(ThreadControl::CROSS_REGION_THREAD, false);
}

void NavigationMowerAlg::CrossRegionEnable()
{
    DealFeatureSelect(ThreadControl::CROSS_REGION_THREAD, true);
}

void NavigationMowerAlg::ControlRotaryMotion(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    float sign = UnifyAngle(yaw_des - yaw_first) >= 0.0 ? 1.0 : -1.0; // Default rotation direction 1.0 left, -1.0 right
    if (sign > 0)
    {
        LOG_INFO("[ControlRotaryMotion1] Rotation direction: left");
    }
    else
    {
        LOG_INFO("[ControlRotaryMotion1] Rotation direction: right");
    }

    float ang_err = fabsf(UnifyAngle(yaw_des - yaw_first));
    uint64_t t = (ang_err / vel_angular) * 1000; // Rotation duration ms
    LOG_INFO("[ControlRotaryMotion1] Rotation angle = {}", Radians2Degrees(ang_err));
    LOG_INFO("[ControlRotaryMotion1] Angular velocity = {}, time = {}", sign * vel_angular, ang_err / vel_angular);
    PublishVelocity(0, sign * vel_angular, t);
}

void NavigationMowerAlg::ControlLinearMotion(const float &pass_point, const float &location,
                                             const float &vel_linear, const int &reverse)
{
    float dis = fabsf(pass_point - location);
    uint64_t t = (dis / vel_linear) * 1000;
    LOG_INFO("[ControlLinearMotion1] Linear distance dis = {}", dis);
    LOG_INFO("[ControlLinearMotion1] Linear speed = {}, time = {}", reverse * vel_linear, dis / vel_linear);
    PublishVelocity(reverse * vel_linear, 0, t);
}

void NavigationMowerAlg::UpdatePoseStuckDetection(const McuExceptionStatus &mcu_exception_status)
{
    if (pose_stuck_detector_ == nullptr)
    {
        PoseStuckConfig pose_stuck_config;
        pose_stuck_detector_ = std::make_unique<PoseStuckDetector>(pose_stuck_config);
    }
    uint64_t time_now_ms = GetSteadyClockTimestampMs();
    bool is_collision = false;
    if (mcu_exception_status == McuExceptionStatus::COLLISION)
    {
        is_collision = true;
    }
    bool is_slip = is_slipping_detected_.load();
    PoseStuckData pose_stuck_data(time_now_ms, fusion_pose_.x, fusion_pose_.y, fusion_pose_.yaw, is_collision, is_slip);
    PoseStuckResult pose_stuck_result;
    pose_stuck_detector_->Detect(pose_stuck_data, &pose_stuck_result);
    bool is_stuck = pose_stuck_result.is_pose_stuck;
    bool is_collision_stuck = pose_stuck_result.is_collision_stuck;
    bool is_slip_stuck = pose_stuck_result.is_slip_stuck;
    if (is_stuck)
    {
        LOG_INFO("pose stuck detected");
        is_stuck_detected_.store(is_stuck);
    }
    if (is_collision_stuck)
    {
        LOG_ERROR("collision stuck detected");
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_mower_node";
        exception.exception_level = mower_msgs::msg::SocExceptionLevel::ERROR;
        exception.exception_value = mower_msgs::msg::SocExceptionValue::ALG_PNC_COLLISION_TRAP_ERROR;
        pub_iceoryx_exception_->publishAtInterval(exception, std::chrono::milliseconds{1000});
    }
    if (is_slip_stuck)
    {
        LOG_ERROR("slip stuck detected");
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_mower_node";
        exception.exception_level = mower_msgs::msg::SocExceptionLevel::ERROR;
        exception.exception_value = mower_msgs::msg::SocExceptionValue::ALG_PNC_SLIP_TRAP_ERROR;
        pub_iceoryx_exception_->publishAtInterval(exception, std::chrono::milliseconds{1000});
    }
}

void NavigationMowerAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_iceoryx_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

void NavigationMowerAlg::PublishSlipException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_iceoryx_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_mower_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_iceoryx_exception_->publishAtInterval(exception, std::chrono::milliseconds{1000});
    }
}

void NavigationMowerAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_mower_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation Mower publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}
//==============================================================================
// Region exploration function handlers
//==============================================================================

void NavigationMowerAlg::ProcessCutBorderUnstakeMode()
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot powered */
        mcu_triggers_cut_border_)                          /* MCU cut border request */
    {
        LOG_INFO("[ProcessCutBorderUnstakeMode] Cut border mode. Start unstake mode");
        PerformUnstakeMode(); // Execute unstake operation
    }
}
void NavigationMowerAlg::ProcessCutBorderUnstakeMode(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_enable_unstake_mode_ && !is_unstake_success_ && /* Unstake mode enabled && not successful */
        is_power_connected_ &&                             /* Robot powered */
        mcu_triggers_cut_border_)                          /* MCU cut border request */
    {
        LOG_INFO("[ProcessCutBorderUnstakeMode] Cut border mode. Start unstake mode");
        PerformUnstakeMode(qrcode_loc_result); // Execute unstake operation
    }
}

void NavigationMowerAlg::HandleCutBorderMcuException(const ExceptionInfo &exception_info)
{
    const auto &recharge_state = exception_info.recharge_state;
    const auto &cross_region_state = exception_info.cross_region_state;
    const auto &is_slipping = exception_info.is_slipping;
    const auto &mcu_exception_status = exception_info.mcu_exception_status;
    const auto &is_stuck = exception_info.is_stuck;
    // If charging station QR code is detected
    if (thread_control_ == ThreadControl::RECHARGE_THREAD &&
        (recharge_state == RechargeRunningState::ADJUST_TO_STATION ||
         recharge_state == RechargeRunningState::ACCURATE_DOCK)) // Charging station QR code found /**Cannot perform recovery mode */
    {
        ProcessCutBorderRechargeException(recharge_state);
    }
    // If beacon QR code is detected (cross-region case)
    else if (thread_control_ == ThreadControl::CROSS_REGION_THREAD &&
             cross_region_state != CrossRegionRunningState::EDGE_FINDING_BEACON &&
             cross_region_state != CrossRegionRunningState::PER_FOUND_BEACON &&
             cross_region_state != CrossRegionRunningState::FINISH &&
             cross_region_state != CrossRegionRunningState::UNDEFINED) // Beacon found /**Cannot perform recovery mode */
    {
        ProcessCutBorderCrossRegionException(cross_region_state);
    }
    // Other exceptions, enter recovery mode
    else
    {
        ProcessRecoveryException(is_slipping, mcu_exception_status, is_stuck);
    }
}

// Handle MCU exception for recharge request logic
void NavigationMowerAlg::ProcessCutBorderRechargeException(RechargeRunningState recharge_state)
{
    (void)recharge_state;
    LOG_WARN_THROTTLE(500, "[BeaconDetection1] Charging station QR code detected");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_cut_border_)                            /* MCU cut border request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection1] Switch to recharge mode");

        // Switch to recharge thread and update feature selection
        thread_control_ = ThreadControl::RECHARGE_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

// Handle MCU exception for cross-region request logic
void NavigationMowerAlg::ProcessCutBorderCrossRegionException(CrossRegionRunningState cross_region_state)
{
    (void)cross_region_state;
    LOG_WARN_THROTTLE(500, "[BeaconDetection1] Beacon QR code detected");

    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        mcu_triggers_cut_border_)                            /* MCU cut border request */
    {
        LOG_INFO_THROTTLE(500, "[BeaconDetection1] Switch to cross-region mode");

        // Switch to cross-region thread and update feature selection
        thread_control_ = ThreadControl::CROSS_REGION_THREAD;
        UpdateFeatureSelection(thread_control_);
    }
}

void NavigationMowerAlg::PerformCutBorder(const MarkLocationResult &mark_loc_result, const QRCodeLocationResult &qrcode_loc_result,
                                          const PerceptionFusionResult &fusion_result,
                                          CrossRegionRunningState &cross_region_state, BehaviorRunningState &behavior_state,
                                          bool is_behavior_loop, const std::vector<BehaviorExceptionType> &triggered_exception_types)
{
    switch (thread_control_)
    {
    case ThreadControl::UNDEFINED:
    case ThreadControl::PERCEPTION_EDGE_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In edge-following or undefined mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In edge-following or undefined mode");

        bool is_beacon_valid = false; // Default: beacon invalid
        ProcessBeaconDetection(mark_loc_result, fusion_result, enter_multi_region_exploration_, is_beacon_valid);

        if (!enter_multi_region_exploration_)
        {
            ProcessSingleAreaCutBorderMode(qrcode_loc_result, enter_multi_region_exploration_);
        }
        else
        {
            ProcessMultiAreaCutBorderMode(mark_loc_result, fusion_result, enter_multi_region_exploration_, is_beacon_valid);
        }

        break;
    }

    case ThreadControl::CROSS_REGION_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In cross-region mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In cross-region mode");

        // Handle different cross-region states
        HandleCutBorderCrossRegionStates(cross_region_state);

        break;
    }

    case ThreadControl::RECHARGE_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In recharge mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In recharge mode");

        ProcessingCutBorderRecharge(qrcode_loc_result);

        if (is_first_cut_border_mode_end_)
        {
            PublishVelocity(0.0, 0.0, 1000);
            is_first_cut_border_mode_end_ = false;
        }

        break;
    }

    case ThreadControl::BEHAVIOR_THREAD:
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In Behavior mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In Behavior mode");

        CheckVelocityAndUpdateState(behavior_state, is_behavior_loop, triggered_exception_types);

        break;
    }

    default:
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In other function mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] In other function mode");

        break;
    }
}

/**
 * @brief Use charging station detection to determine when to stop edge following and switch to recharge
 *
 * @param mark_loc_result
 * @param qrcode_loc_result
 * @param is_beacon_valid
 */
void NavigationMowerAlg::ProcessSingleAreaCutBorderMode(const QRCodeLocationResult &qrcode_loc_result,
                                                        const bool &enter_multi_region_exploration)
{
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        !enter_multi_region_exploration)                     /* Not entering multi-region exploration */
    {
        // LOG_INFO_THROTTLE(1000, "[BeaconDetection1] Start single area cut border mode");
        LOG_INFO_THROTTLE(1000, "[BeaconDetection1] Start single area cut border mode");

        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE)
        {
            LOG_INFO("[BeaconDetection1] QR code pose detected");

            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < recharge_distance_threshold_)
            {
                LOG_INFO("[BeaconDetection1] QR code pose within recharge distance threshold");

                auto current_time = std::chrono::steady_clock::now();
                qr_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_qr_cut_border_detection_time_);

                LOG_INFO("[BeaconDetection1] Charging station QR code detection cooldown timer (seconds): ({})", qr_detection_duration_.count());
                if (qr_detection_duration_.count() > qr_detection_cooldown_time_threshold_) // Increase timer
                {
                    qr_code_detection_count_++;
                    last_qr_cut_border_detection_time_ = std::chrono::steady_clock::now();
                    LOG_INFO("[BeaconDetection1] Detected valid charging station QR code pose, current detection count: {}", qr_code_detection_count_);
                }
            }
            else
            {
                LOG_INFO("[BeaconDetection1] QR code pose not within recharge distance threshold");
            }
        }
        else
        {
            LOG_INFO("[BeaconDetection1] QR code pose not detected");
        }

        // Use QR code detection to determine when to stop edge following and switch to recharge
        if (qr_code_detection_count_ >= 2)
        {
            LOG_INFO("[BeaconDetection1] Detected valid recharge QR code pose twice, switching to recharge mode");
            thread_control_ = ThreadControl::RECHARGE_THREAD;
            // UpdateFeatureSelection(thread_control_);
            is_single_area_recharge_ = true;

            // Reset state
            qr_code_detection_count_ = 1;
        }
    }
}

void NavigationMowerAlg::ProcessMultiAreaCutBorderMode(const MarkLocationResult &mark_loc_result,
                                                       const PerceptionFusionResult &fusion_result,
                                                       const bool &enter_multi_region_exploration,
                                                       bool &is_beacon_valid)
{
    (void)mark_loc_result;
    if ((!is_enable_unstake_mode_ || is_unstake_success_) && /* Unstake mode not enabled or already successful (first stage complete) */
        enter_multi_region_exploration)                      /* Entering multi-region exploration */
    {
        LOG_INFO("[BeaconDetection1] Start multi-region exploration mode");

        if (is_beacon_valid) // Beacon is valid
        {
            // Reset cooldown timestamp and activate cooldown mechanism
            last_cooldown_time_ = std::chrono::steady_clock::now();
            is_cooldown_active_ = true;
            // ResetAndActivateCooldown();

            LOG_INFO("[BeaconDetection1] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
            LOG_INFO("[BeaconDetection1] Current detected mark_id: {}", current_mark_id_);

            // Initialize only on first entry
            if (is_first_enter_last_mark_detection_time_)
            {
                last_mark_detection_time_ = std::chrono::steady_clock::now(); // Beacon detection start timing
                is_first_enter_last_mark_detection_time_ = false;
                LOG_INFO("[BeaconDetection1] Beacon detection start timing");
            }

            auto current_time = std::chrono::steady_clock::now();
            mark_detection_duration_ = std::chrono::duration_cast<std::chrono::seconds>(current_time - last_mark_detection_time_);

            auto current_time_sec = std::chrono::duration_cast<std::chrono::seconds>(current_time.time_since_epoch());
            auto last_mark_detection_time_sec = std::chrono::duration_cast<std::chrono::seconds>(last_mark_detection_time_.time_since_epoch());
            LOG_WARN("[BeaconDetection1] Current timestamp (seconds) current_time_sec({})", current_time_sec.count());
            LOG_WARN("[BeaconDetection1] Last timestamp (seconds) last_mark_detection_time_sec ({})", last_mark_detection_time_sec.count());
            LOG_INFO("[BeaconDetection1] Beacon detection cooldown timer (seconds): ({})", mark_detection_duration_.count());
            if (mark_detection_duration_.count() > mark_detection_cooldown_time_threshold_)
            {
                if (current_mark_id_ == beacon_status_.mark_id) // Same mark_id
                {
                    beacon_status_.beacon_look_count++;
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection1] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection1] Same mark_id, current detected mark_id: {}", current_mark_id_);
                }
                else // Different mark_id
                {
                    beacon_status_ = BeaconStatus(current_mark_id_, 1);
                    last_mark_detection_time_ = std::chrono::steady_clock::now();
                    LOG_WARN("[BeaconDetection1] Detected valid beacon QR code pose, current detection count: {}", beacon_status_.beacon_look_count);
                    LOG_WARN("[BeaconDetection1] Different mark_id, current detected mark_id: {}", current_mark_id_);
                    LOG_WARN("[BeaconDetection1] Different mark_id, last detected mark_id: {}", beacon_status_.mark_id);
                }
            }

            if (beacon_status_.beacon_look_count >= 2)
            {
                // Start cross-region process
                LOG_INFO("[BeaconDetection1] Beacon detected more than twice, starting cross-region process");

                thread_control_ = ThreadControl::CROSS_REGION_THREAD;
                UpdateFeatureSelection(thread_control_);
                EdgeFollowDisable();
                is_single_area_recharge_ = false;

                // Reset state
                next_paired_beacon_id_ = PairNumber(current_mark_id_);
                beacon_status_ = BeaconStatus(next_paired_beacon_id_, 1);
                LOG_INFO("[BeaconDetection1] Next paired beacon id is {}", next_paired_beacon_id_);
            }
            else
            {
                // Continue edge following
                LOG_INFO("[BeaconDetection1] Beacon detection not more than twice, continue");

                // thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD;
                // UpdateFeatureSelection(thread_control_);

                HandleEdgePerceptionBeaconDetection(mark_loc_result, fusion_result, is_cooldown_active_);
                CrossRegionDisable();
            }
        }
    }
}

void NavigationMowerAlg::HandleCutBorderCrossRegionStates(CrossRegionRunningState &cross_region_state)
{
    if (cross_region_state == CrossRegionRunningState::STAGE4_NONGRASS2GRASS_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection1] Exit cross-region from non-grass to grass");

        if (edge_mode_direction_ == 1) // Edge following clockwise
        {
            LOG_INFO("[CrossRegion] Clockwise edge following cross-region not considered");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_); // Turn left
        }
        else // Edge following counterclockwise
        {
            LOG_INFO("[BeaconDetection1] Counterclockwise edge following, turning right by a certain angle");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // Turn right
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection1] Close cross-region, switch to recharge");
            thread_control_ = ThreadControl::RECHARGE_THREAD; // Recharge mode
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1; // Restore default value
        }
        else
        {
            LOG_INFO("[BeaconDetection1] Close cross-region, switch to edge following");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD; // Edge following mode
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now(); // Beacon detection start timing
            // is_first_enter_last_mark_detection_time_ = true; // Reset timer. If you can find the beacon after cross-region, you need to reset the timer
        }

        mcu_triggers_cross_region_ = false;
        UpdateCrossRegionRunningState(CrossRegionRunningState::FINISH);
    }
    else if (cross_region_state == CrossRegionRunningState::STAGE4_BEACON_EXIT_CROSS_REGION)
    {
        LOG_INFO("[BeaconDetection1] Exit cross-region by beacon detection");

        if (edge_mode_direction_ == 1) // Edge following clockwise
        {
            LOG_INFO("[CrossRegion] Clockwise edge following cross-region not considered");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0);
            // ControlRotaryMotion(cross_region_adjust_yaw_, 0.0, mower_angular_); // Turn left
        }
        else // Edge following counterclockwise
        {
            LOG_INFO("[BeaconDetection1] Counterclockwise edge following, turning right by a certain angle!");
            // ControlLinearMotion(cross_region_adjust_displace_, 0.0, mower_linear_, 1);
            // ControlRotaryMotion(0.0, cross_region_adjust_yaw_, mower_angular_); // Turn right
        }

        region_count_++;
        if (region_count_ > 2)
        {
            LOG_INFO("[BeaconDetection1] Close cross-region, switch to recharge");
            thread_control_ = ThreadControl::RECHARGE_THREAD; // Recharge mode
            // UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            region_count_ = 1; // Restore default value
        }
        else
        {
            LOG_INFO("[BeaconDetection1] Close cross-region, switch to edge following");
            thread_control_ = ThreadControl::PERCEPTION_EDGE_THREAD; // Edge following mode
            UpdateFeatureSelection(thread_control_);
            CrossRegionDisable();

            last_mark_detection_time_ = std::chrono::steady_clock::now(); // Beacon detection start timing
            // is_first_enter_last_mark_detection_time_ = true; // Reset timer. If you can find the beacon after cross-region, you need to reset the timer
        }

        mcu_triggers_cross_region_ = false;
        UpdateCrossRegionRunningState(CrossRegionRunningState::FINISH);
    }
}

void NavigationMowerAlg::ProcessingCutBorderRecharge(const QRCodeLocationResult &qrcode_loc_result)
{
    if (is_single_area_recharge_) // Single area recharge, report based on recharge condition
    {
        if (qrcode_loc_result.detect_status == QRCodeDetectStatus::DETECT_QRCODE_HAVE_POSE) // Can calculate charging station QR code pose
        {
            LOG_INFO("[BeaconDetection1] Single area recharge, QR code pose detected");

            if (sqrt(pow(qrcode_loc_result.xyzrpw.x, 2) + pow(qrcode_loc_result.xyzrpw.y, 2)) < recharge_distance_threshold_)
            {
                LOG_INFO("[BeaconDetection1] Single area recharge, QR code pose within threshold distance");

                if (cut_border_result_callback_)
                {
                    cut_border_result_callback_(true, true);
                }

                is_cut_border_mode_start_ = false;
            }
            else
            {
                LOG_INFO("[BeaconDetection1] Single area recharge, QR code pose outside threshold distance");
            }
        }
        else
        {
            LOG_INFO("[BeaconDetection1] Single area recharge, QR code pose not detected");
        }
    }
    else // Multi-region recharge, report directly
    {
        if (cut_border_result_callback_)
        {
            cut_border_result_callback_(true, true);
        }

        is_cut_border_mode_start_ = false;
    }
}

//==============================================================================
// Region exploration function handlers
//==============================================================================

void NavigationMowerAlg::TriggerExceptionPublishing()
{
    exception_start_time_ = std::chrono::steady_clock::now();
    is_publishing_exception_ = true;
}

void NavigationMowerAlg::InitStuckDetectionRecovery()
{
    LOG_INFO("[MowerAlg] Initialize stuck detection and recovery system");

    // Set stuck recovery parameters
    StuckRecoveryParam param;
    param.wheel_radius = wheel_radius_;
    param.wheel_base = wheel_base_;
    param.enable_data_logging = enable_stuck_detection_data_logging_; // Use parameter to control data logging switch
    param.log_file_path = "/userdata/log/stuck_recovery_data.csv";

    // Create stuck detection recovery instance
    stuck_detection_recovery_ = std::make_unique<StuckDetectionRecovery>(param);

    // Set velocity publisher - convert unique_ptr to shared_ptr
    stuck_detection_recovery_->SetVelocityPublisher(std::shared_ptr<VelocityPublisher>(vel_publisher_.get(), [](VelocityPublisher *) {}));

    // Set exception publisher callback
    // stuck_detection_recovery_->SetExceptionPublisher([this](mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value) {
    //     this->PublishException(level, value);
    // });

    stuck_detection_recovery_->SetExceptionPublisher([this](mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value) {
        this->PublishSlipException(level, value);
    });

    // Initialize
    stuck_detection_recovery_->Initialize();

    LOG_INFO("[MowerAlg] Stuck detection data logging: {}", enable_stuck_detection_data_logging_ ? "Enabled" : "Disabled");
}

void NavigationMowerAlg::DeinitStuckDetectionRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] Shutdown stuck detection and recovery system");
        stuck_detection_recovery_->Shutdown();
        stuck_detection_recovery_.reset();
    }
}

bool NavigationMowerAlg::IsStuckDetected()
{
    if (stuck_detection_recovery_)
    {
        return stuck_detection_recovery_->IsStuck();
    }
    return false;
}

bool NavigationMowerAlg::StartStuckRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] Start stuck recovery");
        return stuck_detection_recovery_->StartRecovery();
    }
    return false;
}

void NavigationMowerAlg::StopStuckRecovery()
{
    if (stuck_detection_recovery_)
    {
        LOG_INFO("[MowerAlg] Stop stuck recovery");
        stuck_detection_recovery_->StopRecovery();
    }
}

bool NavigationMowerAlg::IsStuckRecoveryActive()
{
    if (stuck_detection_recovery_)
    {
        return stuck_detection_recovery_->IsRecoveryActive();
    }
    return false;
}

void NavigationMowerAlg::SetStuckDetectionActive(bool active)
{
    if (stuck_detection_recovery_)
    {
        if (active)
        {
            stuck_detection_recovery_->StartDetection();
        }
        else
        {
            stuck_detection_recovery_->StopDetection();
        }
    }
}

void NavigationMowerAlg::SetResetAllStuckStates()
{
    if (stuck_detection_recovery_)
    {
        stuck_detection_recovery_->ResetAllStates();
    }
}

bool NavigationMowerAlg::ShouldPerformStuckDetection()
{
    if (!stuck_detection_recovery_)
    {
        return false;
    }

    // If in recovery success cooldown period, should not perform stuck detection
    if (stuck_detection_recovery_->IsInRecoverySuccessCooldown())
    {
        return false;
    }

    return true;
}

ob_mower_msgs::NavFusionPose NavigationMowerAlg::GetFusionPose() const
{
    // Called externally in a single thread, no need for locking
    return fusion_pose_;
}

// Data validation methods implementation
bool NavigationMowerAlg::CheckPerceptionFusionDataError(const PerceptionFusionResult &fusion_result)
{
    // Check by output time
    uint64_t now_timestamp = std::chrono::steady_clock::now().time_since_epoch().count();
    double fusion_delta_time = (now_timestamp - fusion_result.output_timestamp * 1000000) * 0.000000001; // seconds
    if (fusion_delta_time > 0.5)
    {
        LOG_ERROR_THROTTLE(1000, "[MowerAlg] PerceptionFusion timeout, delta time = {:.3f}s", fusion_delta_time);
        return true;
    }

    // Check by recv time
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("PerceptionFusion");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 2000000; // 2 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("PerceptionFusion", last_time_info, timeout, data_time_info_map_["PerceptionFusion"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationMowerAlg::CheckMarkLocationDataError(const MarkLocationResult &mark_loc_result)
{
    // Check by timestamp
    uint64_t now_timestamp = std::chrono::steady_clock::now().time_since_epoch().count();
    double mark_delta_time = (now_timestamp - mark_loc_result.timestamp * 1000000) * 0.000000001; // seconds
    if (mark_delta_time > 1.0)                                                                    // 1 second timeout for mark location
    {
        LOG_ERROR_THROTTLE(1000, "[MowerAlg] MarkLocation timeout, delta time = {:.3f}s", mark_delta_time);
        return true;
    }

    // Check by recv time
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MarkLocation");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 3000000; // 3 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("MarkLocation", last_time_info, timeout, data_time_info_map_["MarkLocation"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationMowerAlg::CheckQRCodeLocationDataError(const QRCodeLocationResult &qrcode_loc_result)
{
    // Check by timestamp
    uint64_t now_timestamp = std::chrono::steady_clock::now().time_since_epoch().count();
    double qrcode_delta_time = (now_timestamp - qrcode_loc_result.timestamp_ms * 1000000) * 0.000000001; // seconds
    if (qrcode_delta_time > 1.0)                                                                         // 1 second timeout for QR code location
    {
        LOG_ERROR_THROTTLE(1000, "[MowerAlg] QRCodeLocation timeout, delta time = {:.3f}s", qrcode_delta_time);
        return true;
    }

    // Check by recv time
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("QRCodeLocation");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 3000000; // 3 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("QRCodeLocation", last_time_info, timeout, data_time_info_map_["QRCodeLocation"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationMowerAlg::CheckImuDataError()
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("ImuData");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 2000000; // 2 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("ImuData", last_time_info, timeout, data_time_info_map_["ImuData"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationMowerAlg::CheckMcuExceptionDataError()
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("McuException");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 1000000; // 1 second in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("McuException", last_time_info, timeout, data_time_info_map_["McuException"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationMowerAlg::CheckMotorSpeedDataError()
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MotorSpeed");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 2000000; // 2 seconds in microseconds
    auto last_time_info = iter->second;
    CheckTimeout("MotorSpeed", last_time_info, timeout, data_time_info_map_["MotorSpeed"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

void NavigationMowerAlg::CheckTimeout(const std::string &name, const DataTimeInfo &last_time_info,
                                      uint64_t timeout, DataTimeInfo &cur_time_info)
{
    uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
    // Check timeout
    uint64_t delta_recv_time = now_timestamp - last_time_info.recv_timestamp;
    cur_time_info.is_timeout = false;
    if (delta_recv_time > timeout)
    {
        LOG_ERROR_THROTTLE(2000, "[MowerAlg] {} timeout, delta_recv_time: {} us total timeout: {} us",
                           name, delta_recv_time, timeout);
        cur_time_info.is_timeout = true;
    }
}

void NavigationMowerAlg::UpdateDataTimeInfo(const std::string &name, const DataTimeInfo &last_time_info,
                                            uint64_t low_freq_time, uint32_t low_freq_count_max,
                                            uint64_t cur_send_timestamp, DataTimeInfo &cur_time_info)
{
    uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
    // Check low frequency
    uint64_t delta_send_time = cur_send_timestamp - last_time_info.send_timestamp;
    if (last_time_info.send_timestamp > 0 && (delta_send_time == 0 || delta_send_time > low_freq_time))
    {
        cur_time_info.low_freq_count++;
    }
    else
    {
        cur_time_info.low_freq_count = 0;
    }

    cur_time_info.is_low_freq = false;
    if (cur_time_info.low_freq_count > low_freq_count_max)
    {
        LOG_ERROR_THROTTLE(2000, "[MowerAlg] {} low frequency, low_freq_count: {} max: {}",
                           name, cur_time_info.low_freq_count, low_freq_count_max);
        cur_time_info.is_low_freq = true;
    }

    // Update timestamps
    cur_time_info.recv_timestamp = now_timestamp;
    cur_time_info.send_timestamp = cur_send_timestamp;
}

void NavigationMowerAlg::SetPerceptionFusionResult(const PerceptionFusionResult &fusion_result)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("PerceptionFusion");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("PerceptionFusion", last_time_info, low_freq_time, low_freq_count_max,
                       fusion_result.output_timestamp, data_time_info_map_["PerceptionFusion"]);
}

void NavigationMowerAlg::SetMarkLocationResultWithTimeInfo(const MarkLocationResult &mark_loc_result)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MarkLocation");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("MarkLocation", last_time_info, low_freq_time, low_freq_count_max,
                       mark_loc_result.timestamp, data_time_info_map_["MarkLocation"]);
}

void NavigationMowerAlg::SetQRCodeLocationResultWithTimeInfo(const QRCodeLocationResult &qrcode_loc_result)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("QRCodeLocation");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("QRCodeLocation", last_time_info, low_freq_time, low_freq_count_max,
                       qrcode_loc_result.timestamp_ms, data_time_info_map_["QRCodeLocation"]);
}

void NavigationMowerAlg::SetImuDataWithTimeInfo(const ImuData &imu_data)
{
    (void)imu_data; // Suppress unused parameter warning
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("ImuData");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    // Use current timestamp as IMU data might not have a timestamp field
    uint64_t current_timestamp = GetSteadyClockTimestampMs();
    UpdateDataTimeInfo("ImuData", last_time_info, low_freq_time, low_freq_count_max,
                       current_timestamp, data_time_info_map_["ImuData"]);
}

void NavigationMowerAlg::SetMcuExceptionWithTimeInfo(const McuExceptionStatus &mcu_exception_status)
{
    (void)mcu_exception_status; // Suppress unused parameter warning
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("McuException");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    // Use current timestamp as MCU exception status might not have a timestamp field
    uint64_t current_timestamp = GetSteadyClockTimestampMs();
    UpdateDataTimeInfo("McuException", last_time_info, low_freq_time, low_freq_count_max,
                       current_timestamp, data_time_info_map_["McuException"]);
}

void NavigationMowerAlg::SetMotorSpeedDataWithTimeInfo(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MotorSpeed");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 500000; // 500ms in microseconds
    uint32_t low_freq_count_max = 50;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("MotorSpeed", last_time_info, low_freq_time, low_freq_count_max,
                       motor_speed_data.system_timestamp, data_time_info_map_["MotorSpeed"]);
}

bool NavigationMowerAlg::UpdateAndValidateInputData(const MarkLocationResult &mark_loc_result,
                                                    const QRCodeLocationResult &qrcode_loc_result,
                                                    const PerceptionFusionResult &fusion_result,
                                                    McuExceptionStatus &mcu_exception_status)
{
    // Update data time information for all input sources
    SetPerceptionFusionResult(fusion_result);
    SetMarkLocationResultWithTimeInfo(mark_loc_result);
    SetQRCodeLocationResultWithTimeInfo(qrcode_loc_result);
    SetMcuExceptionWithTimeInfo(mcu_exception_status);

    // Update MotorSpeed data time information
    {
        std::lock_guard<std::mutex> lock(motor_speed_mtx_);
        SetMotorSpeedDataWithTimeInfo(motor_speed_data_);
    }

    // Comprehensive input data validation
    if (CheckPerceptionFusionDataError(fusion_result))
    {
        LOG_ERROR_THROTTLE(1000, "[MowerAlg] PerceptionFusion data error detected, stopping motion");
        PublishZeroVelocity();
        return false;
    }

    if (CheckMarkLocationDataError(mark_loc_result))
    {
        LOG_ERROR_THROTTLE(1000, "[MowerAlg] MarkLocation data error detected, stopping motion");
        PublishZeroVelocity();
        return false;
    }

    if (CheckQRCodeLocationDataError(qrcode_loc_result))
    {
        LOG_ERROR_THROTTLE(1000, "[MowerAlg] QRCodeLocation data error detected, stopping motion");
        PublishZeroVelocity();
        return false;
    }

    if (CheckMcuExceptionDataError())
    {
        LOG_ERROR_THROTTLE(1000, "[MowerAlg] MCU exception data error detected, stopping motion");
        PublishZeroVelocity();
        return false;
    }

    if (CheckMotorSpeedDataError())
    {
        LOG_ERROR_THROTTLE(1000, "[MowerAlg] MotorSpeed data error detected, stopping motion");
        PublishZeroVelocity();
        return false;
    }

    return true; // All validations passed
}

// IMU-based control functions implementation
void NavigationMowerAlg::ControlRotaryMotionWithIMU(const float &yaw_des, const float &yaw_first, const float &vel_angular)
{
    if (!imu_processor_ || !imu_processor_->IsBiasCalibrated())
    {
        LOG_WARN("[MowerAlg] IMU processor not ready, falling back to time-based rotation");
        ControlRotaryMotion(yaw_des, yaw_first, vel_angular);
        return;
    }

    float target_angle = UnifyAngle(yaw_des - yaw_first);
    float sign = target_angle >= 0.0f ? 1.0f : -1.0f;

    LOG_INFO("[MowerAlg] Starting IMU-based rotation: target={:.3f}°, direction={}",
             target_angle * 180.0f / M_PI, sign > 0 ? "left" : "right");

    // Start IMU closed-loop control
    imu_processor_->StartRotationControl(target_angle, vel_angular);

    // Wait for rotation to complete
    while (imu_processor_->IsRotationControlActive())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));

        // Check if we need to stop (for example, if a stop signal is received)
        // if (mower_running_state_ == MowerRunningState::PAUSE)
        // {
        //     imu_processor_->StopRotationControl();
        //     break;
        // }
    }

    // Stop velocity after rotation completes
    PublishVelocity(0.0f, 0.0f, 100);
    LOG_INFO("[MowerAlg] IMU-based rotation completed");
}

void NavigationMowerAlg::ControlLinearMotionWithIMUThread(const float &pass_point, const float &location,
                                                          const float &vel_linear, const int &reverse, const float &target_yaw)
{
    // Check if IMU processor is available and calibrated
    if (!imu_processor_ || !imu_processor_->IsBiasCalibrated())
    {
        LOG_WARN("[MowerAlg] IMU processor not available or not calibrated, falling back to basic linear motion");
        ControlLinearMotion(pass_point, location, vel_linear, reverse);
        return;
    }

    float total_distance = fabsf(pass_point - location);
    float expect_velocity = vel_linear * reverse; // Consider direction

    LOG_INFO("[MowerAlg] Starting IMU thread-based linear motion: distance={:.3f}m, velocity={:.3f}m/s, target_yaw={:.3f}°",
             total_distance, expect_velocity, target_yaw * 180.0f / M_PI);

    // Start IMU closed-loop linear motion control
    imu_processor_->StartLinearMotionControl(total_distance, expect_velocity, target_yaw);

    while (imu_processor_->IsLinearMotionControlActive())
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(50));

        // Check if we need to stop (for example, if a stop signal is received)
        // if (mower_running_state_ == MowerRunningState::PAUSE)
        // {
        //     imu_processor_->StopLinearMotionControl();
        //     break;
        // }
    }

    PublishVelocity(0.0f, 0.0f, 100);
    LOG_INFO("[MowerAlg] IMU thread-based linear motion completed");
}

// IMU data processor interface implementation
void NavigationMowerAlg::InitializeImuProcessor()
{
    // Set IMU processor parameters
    imu_processor_param_.filter_alpha = 1.0f;
    imu_processor_param_.angular_velocity_threshold = 0.00f;
    imu_processor_param_.bias_calibration_samples = 300;
    imu_processor_param_.rotation_tolerance = 0.17f; // about 10 degrees
    imu_processor_param_.max_rotation_time = 30.0f;
    imu_processor_param_.backup_distance = 0.3f;      // backup distance 0.3m
    imu_processor_param_.backup_speed = 0.2f;         // backup speed 0.2m/s
    imu_processor_param_.max_backup_attempts = 5;     // max backup attempts 5
    imu_processor_param_.enable_data_logging = false; // enable as needed
    imu_processor_param_.log_file_path = "/userdata/log/mower_imu.log";

    // Create IMU processor
    imu_processor_ = std::make_unique<ImuDataProcessor>(imu_processor_param_);

    // Set velocity publish callback
    imu_processor_->SetVelocityCallback([this](float linear, float angular, uint64_t duration_ms) {
        this->PublishVelocity(linear, angular, duration_ms);
    });

    // Set IMU data callback
    imu_processor_->SetImuDataCallback([this](const ImuData &processed_data) {
        // Handle filtered IMU data here
        LOG_DEBUG("[MowerAlg] Received processed IMU data: angular_velocity_z = {:.4f}",
                  processed_data.angular_velocity_z);
    });

    // Initialize processor
    imu_processor_->Initialize();

    LOG_INFO("[MowerAlg] IMU processor initialized successfully");
}

void NavigationMowerAlg::ShutdownImuProcessor()
{
    if (imu_processor_)
    {
        imu_processor_->Shutdown();
        imu_processor_.reset();
        LOG_INFO("[MowerAlg] IMU processor shutdown completed");
    }
}

bool NavigationMowerAlg::CanTriggerBottomObstacleAvoidance()
{
    auto current_time = std::chrono::steady_clock::now();

    // Check if enough time has passed since the last bottom obstacle avoidance
    if (last_bottom_obstacle_avoidance_time_.time_since_epoch().count() == 0)
    {
        // First time, allow trigger
        return true;
    }

    auto time_since_last_avoidance = std::chrono::duration<float>(current_time - last_bottom_obstacle_avoidance_time_).count();
    bool can_trigger = time_since_last_avoidance >= bottom_obstacle_avoidance_cooldown_time_;

    LOG_INFO_THROTTLE(1000, "[Mower] Bottom obstacle avoidance cooldown check: time_since_last={:.2f}s, cooldown_time={:.2f}s, can_trigger={}",
                      time_since_last_avoidance, bottom_obstacle_avoidance_cooldown_time_, can_trigger);

    return can_trigger;
}

} // namespace fescue_iox
